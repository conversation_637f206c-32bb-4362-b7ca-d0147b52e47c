import React, { useRef } from "react";
import { TableHeader, TableHead, TableRow } from "@/components/ui/table";
import {
  EnhancedTableHeader,
  HeaderTooltipContent,
} from "@/components/EnhancedTableHeader";
import { useLanguage } from "@/contexts/LanguageContext";
import {
  Coins,
  Shield,
  Users,
  BarChart3,
  Lightbulb,
  ListFilter,
  Star,
} from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";

export interface CoinTableHeaderProps {
  sortConfig: {
    key: string;
    direction: "asc" | "desc" | false;
  };
  requestSort: (key: string) => void;
  className?: string;
  id?: string;
}

export const CoinTableHeader: React.FC<CoinTableHeaderProps> = ({
  sortConfig,
  requestSort,
  className,
  id,
}) => {
  const { t } = useLanguage();
  const isMobile = useIsMobile();
  const tableHeaderRef = useRef<HTMLTableSectionElement>(null);

  // Responsive tasarım için ek kontroller burada eklenebilir

  return (
    <TableHeader
      ref={tableHeaderRef}
      id={id || "coin-table-header"}
      className={`${className || ""}`}
    >
      <TableRow className="sticky top-0 z-[999] h-14 bg-[#132F4C]/98 backdrop-blur-md border-b border-[#1E4976]/30 shadow-sm">
        {/* Rank Column Header - Always Visible */}
        <TableHead className="w-[1%]">
          <EnhancedTableHeader
            label=""
            align="center"
            sortable
            isSorted={sortConfig.key === "rank" ? sortConfig.direction : false}
            onSort={() => requestSort("rank")}
          />
        </TableHead>

        {/* Favorite Column Header - Always Visible */}
        <TableHead className="w-[1%] px-0">
          <EnhancedTableHeader
            align="center"
            tooltipContent={
              <HeaderTooltipContent
                title="Favorites"
                description="Add cryptocurrencies to your favorites"
                icon={<Star size={16} className="text-[#66B2FF]" />}
              />
            }
            customLabel={<Star size={14} className="text-gray-400 mx-auto" />}
          />
        </TableHead>

        {/* Name Column Header - Always Visible */}
        <TableHead className="w-[9%] text-left">
          <EnhancedTableHeader
            label={t("coinlist:columns.name", "coinlist", "Name")}
            align="left"
            tooltipContent={
              <HeaderTooltipContent
                title={t("coinlist:tooltips.name.title", "coinlist", "Coin Name & Symbol")}
                description={t("coinlist:tooltips.name.description", "coinlist", "The official name and symbol of the cryptocurrency as listed on exchanges.")}
                icon={<ListFilter className="h-4 w-4" />}
              />
            }
            sortable
            isSorted={sortConfig.key === "name" ? sortConfig.direction : false}
            onSort={() => requestSort("name")}
          />
        </TableHead>

        {/* Tokenomics Column Header - Hidden on Mobile, Visible on MD screens and up */}
        <TableHead className="hidden md:table-cell w-[6%]">
          <EnhancedTableHeader
            label={t("coinlist:columns.tokenomics", "coinlist", "Tokenomics")}
            tooltipContent={
              <HeaderTooltipContent
                title={t("coinlist:tooltips.tokenomics.title", "coinlist", "Tokenomics Analysis")}
                description={t("coinlist:tooltips.tokenomics.description", "coinlist", "Measures token supply mechanisms, inflation risks, and vesting structures.")}
                icon={<Coins className="h-4 w-4" />}
              />
            }
            sortable
            isSorted={
              sortConfig.key === "tokenomics" ? sortConfig.direction : false
            }
            onSort={() => requestSort("tokenomics")}
            align="center"
          />
        </TableHead>

        {/* Security Column Header */}
        <TableHead className="hidden md:table-cell w-[6%]">
          <EnhancedTableHeader
            label={t("coinlist:columns.security", "coinlist", "Security")}
            tooltipContent={
              <HeaderTooltipContent
                title={t("coinlist:tooltips.security.title", "coinlist", "Security Analysis")}
                description={t("coinlist:tooltips.security.description", "coinlist", "Security audit results and risk assessment metrics.")}
                icon={<Shield className="h-4 w-4" />}
              />
            }
            sortable
            isSorted={
              sortConfig.key === "security" ? sortConfig.direction : false
            }
            onSort={() => requestSort("security")}
            align="center"
          />
        </TableHead>

        {/* Social Column Header  */}
        <TableHead className="hidden md:table-cell w-[6%]">
          <EnhancedTableHeader
            label={t("coinlist:columns.social", "coinlist", "Social")}
            tooltipContent={
              <HeaderTooltipContent
                title={t("coinlist:tooltips.social.title", "coinlist", "Social Analysis")}
                description={t("coinlist:tooltips.social.description", "coinlist", "Social media presence, community engagement and sentiment analysis.")}
                icon={<Users className="h-4 w-4" />}
              />
            }
            sortable
            isSorted={
              sortConfig.key === "social" ? sortConfig.direction : false
            }
            onSort={() => requestSort("social")}
            align="center"
          />
        </TableHead>

        {/* Market Column Header */}
        <TableHead className="hidden md:table-cell w-[6%]">
          <EnhancedTableHeader
            label={t("coinlist:columns.market", "coinlist", "Market")}
            tooltipContent={
              <HeaderTooltipContent
                title={t("coinlist:tooltips.market.title", "coinlist", "Market Performance Analysis")}
                description={t("coinlist:tooltips.market.description", "coinlist", "Measures trading volume, liquidity and overall market health.")}
                icon={<BarChart3 className="h-4 w-4" />}
              />
            }
            sortable
            isSorted={
              sortConfig.key === "market" ? sortConfig.direction : false
            }
            onSort={() => requestSort("market")}
            align="center"
          />
        </TableHead>

        {/* Insights Column Header */}
        <TableHead className="hidden md:table-cell w-[6%]">
          <EnhancedTableHeader
            label={t("coinlist:columns.insights", "coinlist", "Insights")}
            tooltipContent={
              <HeaderTooltipContent
                title={t("coinlist:tooltips.insights.title", "coinlist", "AI Insights Analysis")}
                description={t("coinlist:tooltips.insights.description", "coinlist", "AI-powered project insights, sentiment analysis and forecasting metrics.")}
                icon={<Lightbulb className="h-4 w-4" />}
              />
            }
            sortable
            isSorted={
              sortConfig.key === "insights" ? sortConfig.direction : false
            }
            onSort={() => requestSort("insights")}
            align="center"
          />
        </TableHead>

        {/* Total Score Column Header - Always Visible */}
        <TableHead className="w-[7%]">
          <EnhancedTableHeader
            label={
              isMobile
                ? "Score"
                : t("coinlist:columns.totalScore", "coinlist", "Total Score")
            }
            tooltipContent={
              <HeaderTooltipContent
                title={t("coinlist:tooltips.totalScore.title", "coinlist", "Total Score")}
                description={t("coinlist:tooltips.totalScore.description", "coinlist", "The overall rating calculated from all scoring metrics.")}
                icon={<Lightbulb className="h-4 w-4" />}
              />
            }
            sortable
            isSorted={
              sortConfig.key === "totalScore" ? sortConfig.direction : false
            }
            onSort={() => requestSort("totalScore")}
            align="center"
          />
        </TableHead>

        {/* 7D Change Column Header - Always Visible */}
        <TableHead className="w-[6%]">
          <EnhancedTableHeader
            label={t(
              "coinlist:columns.sevenDayChange",
              "coinlist",
              "7d Change",
            )}
            tooltipContent={
              <HeaderTooltipContent
                title={t("coinlist:tooltips.sevenDayChange.title", "coinlist", "7 Day Change")}
                description={t("coinlist:tooltips.sevenDayChange.description", "coinlist", "Percentage price change over the last 7 days.")}
                icon={<BarChart3 className="h-4 w-4" />}
              />
            }
            sortable
            isSorted={
              sortConfig.key === "sevenDayChange" ? sortConfig.direction : false
            }
            onSort={() => requestSort("sevenDayChange")}
            align="center"
          />
        </TableHead>
      </TableRow>
    </TableHeader>
  );
};
