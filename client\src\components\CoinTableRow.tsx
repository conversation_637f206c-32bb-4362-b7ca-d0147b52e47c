import React from "react";
import { useLocation } from "wouter";
import { TableCell, TableRow } from "@/components/ui/table";
import { CoinLogo } from "@/components/CoinLogo";
import { ContextAwareWatchlistButton } from "@/components/watchlists/ContextAwareWatchlistButton";
import { SevenDayScoreBadge } from "@/components/SevenDayScoreBadge";
import UpcomingIdoBadgeRenderer from "@/components/ui/UpcomingIdoBadgeRenderer";
import { UpcomingTotalScoreBadge } from "@/components/UpcomingTotalScoreBadge";
import { cn, formatCoinAge } from "@/lib/utils";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { LazyHoverContent } from "@/components/LazyHoverContent";
import { Coin } from "@/types/CoinTypes";
import { CoinStatus } from "@/types/CoinStatus";
import { useLanguage } from "@/contexts/LanguageContext";
import { useWatchlist } from "@/components/watchlists/WatchlistProvider";
import { removeFromWatchlistService } from "@/lib/services/WatchlistService";
import { Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  getScoreRatingStyles,
  getStatusTranslationInComponent,
  getStatusFromScore,
} from "../utils/scoreUtils";
import {
  coinRowBaseClasses,
  coinRowEvenClass,
  coinRowOddClass,
} from "../styles/coinRowStyles.ts";

interface CoinTableRowProps {
  coin: Coin;
  index: number;
  currentPage: number;
  pageSize: number;
  onRowClick?: (coinId: string) => void;
  showAgeBadge?: boolean;
  showTrashIcon?: boolean;
}

const CoinTableRow = ({
  coin,
  index,
  currentPage,
  pageSize,
  onRowClick,
  showAgeBadge = false,
  showTrashIcon = true,
}: CoinTableRowProps) => {
  const [, setLocation] = useLocation();
  const { t } = useLanguage();
  const { selectedWatchlist, isInWatchlist } = useWatchlist();

  const handleRowClick = () => {
    if (coin.demo) return; // Prevent clicking demo coins

    if (onRowClick) {
      onRowClick(coin.id);
    } else {
      setLocation(`/coin/${coin.id}`);
    }
  };

  const handleRemoveFromWatchlist = async (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    if (!selectedWatchlist) return;

    try {
      await removeFromWatchlistService(selectedWatchlist.id, coin.id);
    } catch (error) {
      console.error("Failed to remove coin from watchlist:", error);
    }
  };

  // Check if coin is in the current watchlist
  const coinInWatchlist = selectedWatchlist && isInWatchlist(coin.id, selectedWatchlist.id);

  return (
    <TableRow
      key={coin.id}
      id={index === 0 ? "coin-list-row-example" : undefined}
      data-tour="coinlist-table-row"
      className={cn(
        coinRowBaseClasses,
        index % 2 === 0 ? coinRowEvenClass : coinRowOddClass,
        coin.demo && "blur-sm opacity-60 pointer-events-none", // Add blur effect for demo coins
      )}
      onClick={handleRowClick}
      onKeyDown={(e) => {
        if (!coin.demo && (e.key === "Enter" || e.key === " ")) {
          e.preventDefault();
          handleRowClick();
        }
      }}
      tabIndex={0}
      role="button"
      aria-label={`View details for ${coin.name}`}
    >
      <TableCell className="font-semibold group-hover/row:bg-[#132F4C]/20 group-hover/row:text-[#E7EBF0] transition-all duration-300 py-3.5 px-1 first:rounded-l-sm text-center">
        <div className="flex items-center justify-center">
          <span className="text-center text-sm font-semibold text-[#E7EBF0] group-hover/row:text-white">
            {(currentPage - 1) * pageSize + index + 1}
          </span>
        </div>
      </TableCell>

      {/* Favori Sütunu - UpcomingProjects ile aynı stilde */}
      <TableCell className="py-3.5 px-1 text-center w-[3%] group-hover/row:bg-[#132F4C]/20 transition-all duration-300">
        <div className="flex items-center justify-center gap-1 px-1">
          <div
            id={index === 0 ? "watchlist-btn-example" : undefined}
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              console.log("Watchlist wrapper clicked for", coin.id);
            }}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                e.stopPropagation();
                e.preventDefault();
              }
            }}
            className="flex items-center justify-center cursor-pointer"
            style={{ isolation: "isolate", zIndex: 10, position: "relative" }}
          >
            <ContextAwareWatchlistButton
              coinId={coin.id}
              variant="icon"
              pageType="coinlist"
              className="watchlist-button transition-all duration-300 hover:scale-110 group-hover/row:opacity-100 opacity-80"
            />
          </div>

          {/* Trash icon - only show when coin is in current watchlist and showTrashIcon is true */}
          {coinInWatchlist && showTrashIcon && (
            <Tooltip delayDuration={300}>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 hover:bg-red-500/20 hover:text-red-400 text-gray-500 transition-all duration-300"
                  onClick={handleRemoveFromWatchlist}
                  aria-label="Remove from watchlist"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">
                Remove from watchlist
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </TableCell>

      {/* Name Column - Styled like UpcomingTableRow */}
      <TableCell className="group-hover/row:bg-[#132F4C]/20 transition-all duration-300 py-3.5 px-2 xl:table-cell name-column w-[10%]">
        <HoverCard openDelay={coin.demo ? 99999 : 300}>
          <HoverCardTrigger asChild>
            <div className={coin.demo ? "w-full" : "cursor-pointer w-full"}>
              <div className="flex items-center ml-1">
                {/* Coin Logo */}
                <div
                  className="w-8 h-8 flex items-center justify-center flex-shrink-0 mr-2"
                  style={{ pointerEvents: "none" }}
                >
                  <CoinLogo
                    symbol={coin.symbol}
                    size="md"
                    className="opacity-90"
                    imageUrl={coin.image}
                    hideText={true}
                  />
                </div>

                {/* Coin Name and Symbol */}
                <div
                  className="flex flex-col"
                  style={{ pointerEvents: "none" }}
                >
                  <div className="flex items-center space-x-1">
                    <span
                      className="text-xs font-medium text-gray-200 group-hover/row:text-[#66B2FF] transition-colors duration-300 tracking-tight text-left truncate max-w-[120px]"
                      title={coin.name}
                    >
                      {coin.name.length > 15 ? `${coin.name.substring(0, 15)}...` : coin.name}
                    </span>
                    <span className="text-[10px] text-left text-gray-500 group-hover/row:text-[#94A3B8] transition-colors duration-300 truncate">
                      {coin.symbol}
                    </span>
                  </div>
                  <div className="flex items-center">
                    {/* Coin age badge - showing relative time format like "X gün önce" */}
                    {showAgeBadge && coin.coin_age && (
                      <span className="inline-flex px-1.5 bg-[#1E293B] text-[9px] font-medium text-white rounded-md transition-colors duration-300 tracking-tight whitespace-nowrap h-[20px] flex items-center">
                        {formatCoinAge(coin.coin_age, true)}
                      </span>
                    )}
                  </div>
                </div>
              </div>

              <div className="block sm:hidden mt-1">
                <div className="flex items-center justify-center gap-2">
                  <div className="w-[70px] text-[10px] px-2 py-[0.85] rounded-md font-semibold transition-colors duration-300 text-center">
                    {coin.totalScore.score} •{" "}
                    {(() => {
                      const score = coin.totalScore.score;
                      // Doğrudan iç sistem CoinStatus değerini hesaplayalım
                      const statusValue = getStatusFromScore(score);

                      // Çeviriyi getStatusTranslationInComponent ile alalım
                      return getStatusTranslationInComponent(statusValue);
                    })()}
                  </div>
                  <SevenDayScoreBadge
                    scoreChange={coin.sevenDayChange}
                    className="h-[24px] w-[70px]"
                    alwaysColored={false}
                  />
                </div>
              </div>
            </div>
          </HoverCardTrigger>
          {!coin.demo && (
            <HoverCardContent
              className="w-80 bg-[#132F4C]/95 border border-[#1E4976]/60 backdrop-blur-md overflow-visible rounded-md z-[9999]"
              sideOffset={8}
              side="right"
              align="start"
              avoidCollisions={true}
            >
              <LazyHoverContent
                coinId={coin.id}
                initialData={{
                  name: coin.name,
                  symbol: coin.symbol,
                  rank: coin.rank,
                  image: coin.image,
                }}
              />
            </HoverCardContent>
          )}
        </HoverCard>
      </TableCell>

      {/* Tokenomics Column */}
      <TableCell className="table-cell pl-12 md:pl-4 py-3.5 transition-all duration-300 group-hover/row:bg-[#132F4C]/30 text-center w-[6%]">
        <div className="flex justify-center items-center w-full h-full">
          <div className="flex items-center justify-center gap-1.5 my-auto group/score cursor-pointer">
            <div className="relative transition-all duration-300 rounded-lg p-1 flex items-center gap-1.5">
              <div className="flex-shrink-0">
                <UpcomingIdoBadgeRenderer
                  score={coin.tokenomics.score}
                  value=""
                  badgeType="initialCap"
                />
              </div>
            </div>
          </div>
        </div>
      </TableCell>

      {/* Security Column */}
      <TableCell className="table-cell ml-4 py-3.5 transition-all duration-300 group-hover/row:bg-[#132F4C]/25 text-center w-[6%]">
        <div className="flex justify-center items-center w-full h-full">
          <div className="flex items-center justify-center gap-1.5 my-auto group/score cursor-pointer">
            <div className="relative transition-all duration-300 rounded-lg p-1 flex items-center gap-1.5">
              <div className="flex-shrink-0">
                <UpcomingIdoBadgeRenderer
                  score={coin.security.score}
                  value=""
                  badgeType="investors"
                />
              </div>
            </div>
          </div>
        </div>
      </TableCell>

      {/* Social Column */}
      <TableCell className="table-cell ml-4 py-3.5 transition-all duration-300 group-hover/row:bg-[#132F4C]/20 text-center w-[6%]">
        <div className="flex justify-center items-center w-full h-full">
          <div className="flex items-center justify-center gap-1.5 my-auto group/score cursor-pointer">
            <div className="relative transition-all duration-300 rounded-lg p-1 flex items-center gap-1.5">
              <div className="flex-shrink-0">
                <UpcomingIdoBadgeRenderer
                  score={coin.social.score}
                  value=""
                  badgeType="investors"
                />
              </div>
            </div>
          </div>
        </div>
      </TableCell>

      {/* Market Column */}
      <TableCell className="table-cell ml-4 py-3.5 transition-all duration-300 group-hover/row:bg-[#132F4C]/25 text-center w-[6%]">
        <div className="flex justify-center items-center w-full h-full">
          <div className="flex items-center justify-center gap-1.5 my-auto group/score cursor-pointer">
            <div className="relative transition-all duration-300 rounded-lg p-1 flex items-center gap-1.5">
              <div className="flex-shrink-0">
                <UpcomingIdoBadgeRenderer
                  score={coin.market.score}
                  value=""
                  badgeType="raised"
                />
              </div>
            </div>
          </div>
        </div>
      </TableCell>

      {/* Insights Column */}
      <TableCell className="table-cell ml-4 py-3.5 transition-all duration-300 group-hover/row:bg-[#132F4C]/20 text-center w-[6%]">
        <div className="flex justify-center items-center w-full h-full">
          <div className="flex items-center justify-center gap-1.5 my-auto group/score cursor-pointer">
            <div className="relative transition-all duration-300 rounded-lg p-1 flex items-center gap-1.5">
              <div className="flex-shrink-0">
                <UpcomingIdoBadgeRenderer
                  score={coin.insights.score}
                  value=""
                  badgeType="launchpad"
                />
              </div>
            </div>
          </div>
        </div>
      </TableCell>

      {/* Total Score Column */}
      <TableCell
        id={index === 0 ? "total-score-example" : undefined}
        className="table-cell py-3.5 transition-all duration-300 group-hover/row:bg-[#132F4C]/30 text-center w-[8%]"
      >
        <div className="flex items-center justify-center h-full">
          <div className="flex items-center justify-center gap-1.5 my-auto">
            <div className="relative transition-all duration-300 rounded-lg p-1 flex items-center gap-1.5">
              <div className="flex-shrink-0">
                <div className="relative flex items-center justify-center w-10 h-10">
                  <svg
                    className="w-10 h-10 rotate-[-90deg]"
                    viewBox="0 0 36 36"
                  >
                    <circle
                      cx="18"
                      cy="18"
                      r="16"
                      fill="none"
                      stroke="#1E4976"
                      strokeWidth="2.1"
                      strokeOpacity="0.3"
                    ></circle>
                    <circle
                      cx="18"
                      cy="18"
                      r="16"
                      fill="none"
                      stroke={
                        coin.totalScore.score >= 85
                          ? "#00D88A"
                          : coin.totalScore.score >= 75
                            ? "#00B8D9"
                            : coin.totalScore.score >= 65
                              ? "#FFAB00"
                              : coin.totalScore.score >= 50
                                ? "#FF5630"
                                : "#FF3B3B"
                      }
                      strokeWidth="2.8"
                      strokeLinecap="round"
                      strokeDasharray="100 100"
                      strokeDashoffset={100 - coin.totalScore.score}
                    ></circle>
                  </svg>
                  <div
                    className={`absolute text-sm font-bold ${
                      coin.totalScore.score >= 85
                        ? "text-[#00D88A]"
                        : coin.totalScore.score >= 75
                          ? "text-[#00B8D9]"
                          : coin.totalScore.score >= 65
                            ? "text-[#FFAB00]"
                            : coin.totalScore.score >= 50
                              ? "text-[#FF5630]"
                              : "text-[#FF3B3B]"
                    }`}
                  >
                    <span>{coin.totalScore.score}</span>
                  </div>
                </div>
              </div>
              {(() => {
                const score = coin.totalScore.score;
                // Use the centralized status calculation and translation
                const statusValue = getStatusFromScore(score);
                const statusText = getStatusTranslationInComponent(statusValue);

                return (
                  <div
                    className={cn(
                      "inline-flex items-center justify-center rounded-md border px-2 py-1",
                      "transition-all duration-300 h-7 w-[110px] text-xs font-semibold tracking-tight",
                      score >= 85
                        ? "bg-[#00D88A]/10 text-[#00D88A] border-[#00D88A]/30"
                        : score >= 75
                          ? "bg-[#00B8D9]/10 text-[#00B8D9] border-[#00B8D9]/30"
                          : score >= 65
                            ? "bg-[#FFAB00]/10 text-[#FFAB00] border-[#FFAB00]/30"
                            : score >= 50
                              ? "bg-[#FF5630]/10 text-[#FF5630] border-[#FF5630]/30"
                              : "bg-[#FF3B3B]/10 text-[#FF3B3B] border-[#FF3B3B]/30"
                    )}
                  >
                    {score} {statusText}
                  </div>
                );
              })()}
            </div>
          </div>
        </div>
      </TableCell>

      {/* Change Column */}
      <TableCell className="table-cell py-3.5 transition-all duration-300 group-hover/row:bg-[#132F4C]/20 text-center w-[8%]">
        <div className="flex justify-center items-center w-full h-full">
          <SevenDayScoreBadge
            scoreChange={coin.sevenDayChange}
            className="h-[32px] w-[90px]"
            alwaysColored={false}
          />
        </div>
      </TableCell>
    </TableRow>
  );
};

export default CoinTableRow;