import React, { useState } from "react";
import BadgeComponent from "@/components/BadgeComponent";
import BadgeScore from "@/components/ui/badge-score";
import { Star } from "lucide-react";
import { UpcomingCoin } from "@/services/upcomingApi";
import { toggleFavorite } from "@/services/upcomingApi";
import { CoinLogo } from "@/components/CoinLogo";
import { coinRowBaseClasses, coinRowEvenClass, coinRowOddClass } from '../styles/coinRowStyles';
import { cn } from "@/lib/utils";
import TableScoreGauge from "@/components/ui/TableScoreGauge";

// Puanların durumunu belirten enum (geriye dönük uyumluluk için)
enum RatingStatus {
  EXCELLENT = "Harika",
  POSITIVE = "Olumlu",
  AVERAGE = "Ortalama",
  WEAK = "Zayıf",
  CRITICAL = "Kritik",
}

// Geçiş için eski tip tanımı
interface UpcomingProject {
  id: string;
  name: string;
  symbol: string;
  icon?: React.ReactNode;
  initialCap: number;
  raised: number;
  launchpad: string;
  investors: string[];
  status: string;
  date: string;
  launchType?: string; // ICO, IDO, EVET
}

interface UpcomingTableRowProps {
  item: UpcomingProject;
  upcomingCoin?: UpcomingCoin; // API'dan gelen yeni veri modeli
  formatCurrency: (value: number) => string;
  handleFilterClick: (type: string, value: any) => void;
  handleShowAllInvestors: (investors: string[]) => void;
  index?: number;
}

// Puanları durumlarına göre belirleme yardımcı fonksiyonu
const getScoreStatus = (score: number): RatingStatus => {
  if (score >= 85) return RatingStatus.EXCELLENT;
  if (score >= 75) return RatingStatus.POSITIVE;
  if (score >= 65) return RatingStatus.AVERAGE;
  if (score >= 50) return RatingStatus.WEAK;
  return RatingStatus.CRITICAL;
};

// Puanın durumuna göre renk sınıflarını döndürme
const getScoreColorClasses = (status: RatingStatus | string) => {
  // String olarak gelen status değerlerini desteklemek için
  const statusStr = typeof status === "string" ? status.toLowerCase() : "";

  // RatingStatus enum değeri ya da string kontrolü
  if (
    status === RatingStatus.EXCELLENT ||
    statusStr.includes("harika") ||
    statusStr.includes("excellent")
  ) {
    return {
      badge: "bg-[#00D88A]/20 text-[#00D88A] border-[#00D88A]/30",
      circle: "text-[#00D88A] border-[#00D88A]",
    };
  } else if (
    status === RatingStatus.POSITIVE ||
    statusStr.includes("olumlu") ||
    statusStr.includes("good") ||
    statusStr.includes("positive")
  ) {
    return {
      badge: "bg-[#00B8D9]/20 text-[#00B8D9] border-[#00B8D9]/30",
      circle: "text-[#00B8D9] border-[#00B8D9]",
    };
  } else if (
    status === RatingStatus.AVERAGE ||
    statusStr.includes("orta") ||
    statusStr.includes("fair") ||
    statusStr.includes("average")
  ) {
    return {
      badge: "bg-[#FFAB00]/20 text-[#FFAB00] border-[#FFAB00]/30",
      circle: "text-[#FFAB00] border-[#FFAB00]",
    };
  } else if (
    status === RatingStatus.WEAK ||
    statusStr.includes("zayıf") ||
    statusStr.includes("poor") ||
    statusStr.includes("weak")
  ) {
    return {
      badge: "bg-[#FF5630]/20 text-[#FF5630] border-[#FF5630]/30",
      circle: "text-[#FF5630] border-[#FF5630]",
    };
  } else {
    return {
      badge: "bg-[#FF3B3B]/20 text-[#FF3B3B] border-[#FF3B3B]/30",
      circle: "text-[#FF3B3B] border-[#FF3B3B]",
    };
  }
};

const UpcomingTableRow: React.FC<UpcomingTableRowProps> = ({
  item,
  upcomingCoin,
  formatCurrency,
  handleFilterClick,
  handleShowAllInvestors,
  index = 0,
}) => {
  // Ortak hücre stilleri
  const cellClassName = "border-b border-[#0D2137] py-4 px-2";

  // Favorileri izlemek için state
  const [isFavorite, setIsFavorite] = useState(
    upcomingCoin?.isFavorite || false,
  );
  const [isTogglingFavorite, setIsTogglingFavorite] = useState(false);

  // Favori durumunu değiştiren fonksiyon
  const handleToggleFavorite = async () => {
    if (isTogglingFavorite) return; // Eğer işlem devam ediyorsa çıkış yap

    try {
      setIsTogglingFavorite(true);
      const newFavoriteStatus = !isFavorite;

      // API çağrısı
      const success = await toggleFavorite(coin.id, newFavoriteStatus);

      if (success) {
        setIsFavorite(newFavoriteStatus);
        console.log(
          `${coin.name} ${newFavoriteStatus ? "favorilere eklendi" : "favorilerden çıkarıldı"}`,
        );
      } else {
        console.error("Favori durumu değiştirilemedi");
      }
    } catch (error) {
      console.error("Favori durumu değiştirilirken hata oluştu:", error);
    } finally {
      setIsTogglingFavorite(false);
    }
  };

  // Önce API verilerini veya eski verileri kullan
  const coin = upcomingCoin || {
    id: item.id,
    name: item.name,
    symbol: item.symbol,
    launchDate: item.date,
    launchType: item.launchType || "IDO",
    imcScore: {
      score: item.initialCap,
      status: getScoreStatus(item.initialCap),
      valueText: `${formatCurrency(item.initialCap)}`,
    },
    financingScore: {
      score: item.raised,
      status: getScoreStatus(item.raised),
      valueText: `${formatCurrency(item.raised)}`,
    },
    launchpadScore: {
      score: 80,
      status: getScoreStatus(80),
      valueText: item.launchpad,
    },
    investorScore: {
      score: 75,
      status: getScoreStatus(75),
      valueText: `${item.investors.length} yatırımcı`,
    },
    totalAiScore: 85,
  };

  // Detay URL'si - IDO detay sayfasına yönlendir
  const detailsUrl = `/ido/${item.id}`;

  return (
    <tr className={cn(
      coinRowBaseClasses,
      index % 2 === 0 ? coinRowEvenClass : coinRowOddClass
    )}>
      {/* # Sütunu */}
      <td className={`${cellClassName} text-center text-gray-400 w-8`}>
        <div className="flex items-center justify-center">{index + 1}</div>
      </td>

      {/* Favori Sütunu */}
      <td className={`${cellClassName} text-center w-8 px-0`}>
        <div className="flex items-center justify-center">
          <button
            onClick={handleToggleFavorite}
            disabled={isTogglingFavorite}
            className={`group rounded-full p-1 hover:bg-gray-800 transition-colors ${isTogglingFavorite ? "opacity-50 cursor-not-allowed" : ""}`}
          >
            <Star
              size={16}
              className={`${
                isFavorite
                  ? "fill-yellow-400 text-yellow-400"
                  : "text-gray-600/50 group-hover:text-gray-400"
              } transition-colors duration-300`}
            />
          </button>
        </div>
      </td>

      {/* İsim Sütunu */}
      <td className={`${cellClassName} px-2`}>
        <div className="flex items-center" style={{ pointerEvents: "none" }}>
          <div className="w-6 h-6 flex items-center justify-center flex-shrink-0 mr-2">
            {/* CoinLogo bileşenini kullan - logoyu doğrudan geçir */}
            <CoinLogo
              symbol={coin.symbol}
              size="sm"
              className="opacity-90"
              imageUrl={coin.logo} // API'den gelen logo URL'ini kullan
            />
          </div>
          <div className="flex flex-col">
            <span className="text-xs font-medium text-gray-200">
              {coin.name}
            </span>
            <span className="text-[10px] text-gray-500">{coin.symbol}</span>
          </div>
        </div>
      </td>

      {/* Lansman Tarihi Sütunu */}
      <td className={`${cellClassName} text-center`}>
        <div style={{ pointerEvents: "none" }}>
          <div className="text-xs font-medium text-gray-200">
            {coin.launchDate}
          </div>
          <div className="text-[10px] text-gray-500">{coin.launchType}</div>
        </div>
      </td>

      {/* IMC Puanı Sütunu */}
      <td className={`${cellClassName} text-center`}>
        <div className="flex justify-center">
          <BadgeScore
            score={coin.imcScore.score}
            status={coin.imcScore.status}
            valueText={coin.imcScore.valueText}
            compact={false}
          />
        </div>
      </td>

      {/* Finansman Puanı Sütunu */}
      <td className={`${cellClassName} text-center`}>
        <div className="flex justify-center">
          <BadgeScore
            score={coin.financingScore.score}
            status={coin.financingScore.status}
            valueText={coin.financingScore.valueText}
            compact={false}
          />
        </div>
      </td>

      {/* Launchpad Puanı Sütunu */}
      <td className={`${cellClassName} text-center`}>
        <div className="flex justify-center">
          <BadgeScore
            score={coin.launchpadScore.score}
            status={coin.launchpadScore.status}
            valueText={coin.launchpadScore.valueText}
            compact={false}
          />
        </div>
      </td>

      {/* Yatırımcı Puanı Sütunu */}
      <td className={`${cellClassName} text-center`}>
        <div className="flex justify-center">
          <BadgeScore
            score={coin.investorScore.score}
            status={coin.investorScore.status}
            valueText={coin.investorScore.valueText}
            compact={false}
          />
        </div>
      </td>

      {/* Toplam AI Puanı Sütunu */}
      <td className={`${cellClassName} text-center`}>
        <div className="flex justify-center items-center w-full h-full">
          <div className="flex items-center justify-center gap-1.5 my-auto group/score cursor-pointer">
            <div className="relative transition-all duration-300 rounded-lg p-1 flex items-center gap-1.5">
              <div className="flex-shrink-0">
                <TableScoreGauge score={coin.totalAiScore} />
              </div>
            </div>
          </div>
        </div>
      </td>

      {/* Detaylar Sütunu */}
      <td className={`${cellClassName} text-center`}>
        <div className="flex justify-center">
          <a
            href={detailsUrl}
            className="px-3 py-1 bg-[#00D88A]/20 text-[#00D88A] rounded-md text-xs font-medium hover:bg-[#00D88A]/30 transition-colors duration-300"
          >
            View Details
          </a>
        </div>
      </td>
    </tr>
  );
};

export default UpcomingTableRow;
