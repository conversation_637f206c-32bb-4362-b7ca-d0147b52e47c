import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/hooks/use-auth";
import { useLocation } from "wouter";
import { Lock, UserCircle, ArrowLeft } from "lucide-react";
import { useState } from "react";
import { LoginForm } from "./LoginForm";
import { useLanguage } from "@/contexts/LanguageContext";

interface AuthenticationPromptProps {
  title?: string;
  description?: string;
  returnTo?: string;
  showLoginForm?: boolean;
}

export function AuthenticationPrompt({
  title,
  description,
  returnTo,
  showLoginForm = false
}: AuthenticationPromptProps) {
  const [, setLocation] = useLocation();
  const [showForm, setShowForm] = useState(showLoginForm);
  const { t } = useLanguage();

  // Use translations with fallbacks
  const displayTitle = title || t("auth:authentication.required", "auth", "Authentication Required");
  const displayDescription = description || t("auth:authentication.required.description", "auth", "Please log in to access this feature");

  const handleLogin = () => {
    const currentPath = window.location.pathname;
    setLocation(`/login?returnTo=${encodeURIComponent(returnTo || currentPath)}`);
  };

  const handleGoBack = () => {
    if (window.history.length > 1) {
      window.history.back();
    } else {
      setLocation("/");
    }
  };

  return (
    <div className="flex items-center justify-center mt-12 px-4">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-4">
            <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
              <Lock className="h-6 w-6 text-primary" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-center">{displayTitle}</CardTitle>
          <CardDescription className="text-center">{displayDescription}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {showForm ? (
            <LoginForm />
          ) : (
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground text-center">
                {t("auth:authentication.signInPrompt", "auth", "Sign in to access personalized features, save your preferences, and unlock the full capabilities of CoinScout.")}
              </p>

              <div className="flex flex-col space-y-3">
                <Button
                  onClick={handleLogin}
                  className="w-full"
                  size="lg"
                >
                  <UserCircle className="mr-2 h-4 w-4" />
                  {t("auth:authentication.signin", "auth", "Sign in")}
                </Button>

                <Button
                  variant="outline"
                  onClick={() => setShowForm(true)}
                  className="w-full"
                >
                  {t("auth:authentication.continueWithEmail", "auth", "Continue with email")}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button
            variant="ghost"
            onClick={handleGoBack}
            className="w-full"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("auth:authentication.goBack", "auth", "Go back")}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}