import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useLocation } from "wouter";
import { motion } from "framer-motion";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import { Eye, EyeOff } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

interface LoginFormProps {
  returnTo?: string;
  onSuccess?: () => void;
}

export function LoginForm({ returnTo, onSuccess }: LoginFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [location, setLocation] = useLocation();
  const { login } = useAuth();
  const { toast } = useToast();
  const { t } = useLanguage();

  // Create schema with translations
  const loginSchema = z.object({
    email: z.string().email(t("auth:validation.email")),
    password: z.string().min(6, t("auth:validation.password.length")),
    remember: z.boolean().optional().default(false)
  });

  type LoginFormData = z.infer<typeof loginSchema>;

  // Get returnTo from URL if not provided as prop
  const [returnPath, setReturnPath] = useState<string | null>(returnTo || null);

  useEffect(() => {
    // Parse return URL from query parameters if not already set
    if (!returnPath) {
      const params = new URLSearchParams(window.location.search);
      const returnParam = params.get('returnTo');
      if (returnParam) {
        setReturnPath(returnParam);
      }
    }
  }, [returnPath]);

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
      remember: false
    },
  });

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    try {
      await login(data.email, data.password, data.remember);

      // Show success toast
      toast({
        title: t("auth:login.success.title"),
        description: t("auth:login.success.description"),
        variant: "default",
      });

      // If onSuccess callback provided, call it
      if (onSuccess) {
        onSuccess();
        return;
      }

      // Navigate to the return path or dashboard
      if (returnPath) {
        setLocation(decodeURIComponent(returnPath));
      } else {
        setLocation("/");
      }
    } catch (error: any) {
      console.error("Login error:", error);

      // Check if this is an email verification error from AuthService
      if ((error as any).emailNotVerified === true) {
        console.log("Email not verified, redirecting to verification request page");
        setLocation("/request-email-verification");
        return;
      }

      // Show backend error message if available
      let errorMessage = t("auth:login.error.unknown");

      // Check if the error has a response with errormsg
      if (error.response?.data?.errormsg) {
        errorMessage = error.response.data.errormsg;
      }
      // Check if error has any message property
      else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: t("auth:login.failed", "auth", "Login Failed"),
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="w-full max-w-md mx-auto space-y-6"
    >
      <div className="space-y-2 text-center">
        <h1 className="text-2xl font-bold tracking-tight">{t("auth:welcome.back")}</h1>
        <p className="text-sm text-muted-foreground">
          {t("auth:login.credential.prompt")}
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("auth:email")}</FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("auth:email.placeholder")}
                    type="email"
                    disabled={isLoading}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center justify-between w-full">
                  <FormLabel>{t("auth:password")}</FormLabel>
                  <a
                    href="#"
                    className="text-xs text-muted-foreground hover:text-primary"
                    onClick={(e) => {
                      e.preventDefault();
                      alert(t("auth:password.reset.message"));
                    }}
                  >
                    {t("auth:forgotPassword")}
                  </a>
                </div>
                <FormControl>
                  <div className="relative">
                    <Input
                      placeholder={t("auth:password.placeholder")}
                      type={showPassword ? "text" : "password"}
                      disabled={isLoading}
                      className="pr-10"
                      {...field}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-full px-3"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <Eye className="h-4 w-4 text-muted-foreground" />
                      )}
                      <span className="sr-only">
                        {showPassword ? t("auth:password.hide") : t("auth:password.show")}
                      </span>
                    </Button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="remember"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center space-x-2 space-y-0 mb-4">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    id="remember"
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel htmlFor="remember" className="text-sm font-normal cursor-pointer">
                    {t("remember", "auth", "Remember me for 30 days")}
                  </FormLabel>
                </div>
              </FormItem>
            )}
          />

          <Button
            type="submit"
            className="w-full"
            disabled={isLoading}
          >
            {isLoading ? t("signin.loading", "auth", "Signing in...") : t("signin.securely", "auth", "Sign in securely")}
          </Button>
        </form>
      </Form>
    </motion.div>
  );
}