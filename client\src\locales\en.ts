/**
 * English localization strings
 */
const en = {
  // Navigation namespace
  navigation: {
    Pricing: "Pricing",
    Documentation: "Documentation",
    pricing: "Pricing",
    goToApp: "Go to App",
    goToHomepage: "Go to Homepage",
    coinScoutAlt: "CoinScout Logo"
  },

  // Footer namespace
  footer: {
    description: "AI-powered cryptocurrency analysis platform helping investors make smarter decisions with comprehensive market insights.",
    categories: {
      product: "Product",
      learn: "Learn",
      community: "Community",
      legal: "Legal"
    },
    links: {
      soon: "Soon"
    },
    allRightsReserved: "All rights reserved."
  },

  // System namespace
  system: {
    language: {
      selector: {
        title: "Language Settings",
        label: "Select Language",
        search: "Search languages...",
        available: "Available Languages"
      }
    }
  },

  // Logs for debugging and development
  "logs": {
    "usingDirectAPIData": "🔄 Using direct API data, data length:",
    "imageLoadError": "🖼️ Error loading image:",
    "usingIdoWatchlists": "Using IDO watchlists for upcoming projects:",
    "rawAPIData": "🟡 Raw data from API:",
    "apiDataLength": "🟡 API data length:",
    "filteredData": "🔍 Filtered data:",
    "filteredDataLength": "🔍 Filtered data length:",
    "imageUrlChecks": "🖼️ Image URL checks:",
    "allProjectsFromAPI": "📌 All projects from API:",
    "usingDirectAPI": "🟢 Using direct API data",
    "rawAPIDataFirst5": "🔍 Raw API data (first 5 items):",
    "emptyAPIResult": "⚠️ API returned empty result, using empty array",
    "errorUsingEmptyArray": "❌ Using empty array due to error",
    "totalAIScoreClicked": "Total AI Score clicked:",
    "filtersChanged": "Filters changed:",
    "upcomingIDOFirstUseEffect": "🔄 UpcomingIDO - First useEffect triggered",
    "fetchInitialDataStarted": "🔄 fetchInitialData started",
    "fetchInitialDataCompleted": "🔄 fetchInitialData completed",
    "filterRunning": "🔍 Filter running, coins length:",
    "paginatedData": "📄 Paginated data:",
    "first2Records": "📄 First 2 records:",
    "paginatedDataImageFields": "📄 Image fields in paginated data:",
    "firstRecordDetails": "🧩 First record details:",
    "firstRecordSocialScore": "📢 First record social score value:",
    "clickedProjectInfo": "Clicked project info:",
    "redirectingWithDirectID": "Redirecting with direct ID:"
  },
  // Common strings used throughout the application
  "common": {
    "loading": "Loading...",
    "error": "Error",
    "retry": "Retry",
    "save": "Save",
    "cancel": "Cancel",
    "close": "Close",
    "success": "Success",
    "viewMore": "View More",
    "back": "Back",
    "next": "Next",
    "search": "Search",
    "searchCoinsAndTokens": "Search for Coins & Upcoming Projects",
    "searching": "Searching...",
    "noResults": "No results found",
    "coins": "Coins",
    "upcomingIdos": "Upcoming IDOs"
  },

  // Old format (for backward compatibility)
  "common:loading": "Loading...",
  "common:error": "Error",
  "common:retry": "Retry",
  "common:save": "Save",
  "common:cancel": "Cancel",
  "common:close": "Close",
  "common:success": "Success",
  "common:viewMore": "View More",
  "common:back": "Back",
  "common:next": "Next",
  "common:search": "Search",
  "common:searchCoinsAndTokens": "Search for Coins & Upcoming Projects",
  "common:searching": "Searching...",
  "common:noResults": "No results found",
  "common:coins": "Coins",
  "common:upcomingIdos": "Upcoming IDOs",

  // Navigation items
  "nav": {
    "home": "Home",
    "portfolio": "Portfolio",
    "explore": "Explore",
    "news": "News",
    "learn": "Learn",
    "profile": "Profile",
    "settings": "Settings",
    "logout": "Logout",
    "login": "Login",
    "register": "Register",
    "trending": "Trending",
    "favorites": "Favorites",
    "watchlist": "Watchlist"
  },



  // Homepage content
  "homepage": {
    "hero": {
      "badge": "AI-Powered Crypto Analysis",
      "title": "Make smarter crypto investments",
      "titleHighlight": "with AI insights",
      "description": "CoinScout analyzes over 3,000 cryptocurrencies across 40+ metrics to help you identify the best investment opportunities before others see them.",
      "ctaButton": "Start Free Analysis",
      "stats": {
        "cryptocurrencies": "Cryptocurrencies",
        "analysisMetrics": "Analysis Metrics",
        "moreAccuracy": "More Accuracy"
      }
    },
    "features": {
      "title": "Unlock Superior Investment Decisions",
      "subtitle": "Our comprehensive suite of AI-powered tools helps you navigate the crypto market with confidence",
      "aiRating": {
        "title": "AI-Enhanced Crypto Rating",
        "description": "Proprietary algorithms assess 50+ metrics across tokenomics, security, social engagement, and market factors",
        "bullets": [
          "Comprehensive scoring system",
          "In-depth security risk assessment",
          "Advanced tokenomics evaluation"
        ]
      },
      "idoRating": {
        "title": "IDO Rating",
        "description": "Discover and evaluate promising token offerings before their launch with detailed project analysis and scoring",
        "bullets": [
          "Project score & Risk Analysis",
          "Launchpad tracking & evaluation",
          "Fundraising details & Tokenomics"
        ]
      },
      "compareCoins": {
        "title": "Compare Coins",
        "description": "Side-by-side analysis of multiple cryptocurrencies to identify the best investment opportunities based on your criteria",
        "bullets": [
          "Multi-factor comparison",
          "Relative strength analysis",
          "Historical performance tracking"
        ]
      },
      "portfolioGenerator": {
        "title": "Custom Portfolio Generator",
        "description": "AI-powered recommendations to build a diversified portfolio based on your risk tolerance and investment goals",
        "bullets": [
          "AI-driven portfolio customization",
          "Personalized risk tolerance adjustments",
          "Automated portfolio optimization"
        ]
      },
      "portfolioAnalysis": {
        "title": "AI Portfolio Analysis",
        "description": "Gain AI-powered insights into your existing portfolio with automated risk analysis, diversification score, and optimization tips",
        "bullets": [
          "Portfolio health score analysis",
          "Risk exposure assessment",
          "Smart portfolio balancing insights"
        ]
      },
      "launchpads": {
        "title": "Top Launchpads",
        "description": "Discover the best platforms for new token offerings with performance metrics, success rates, and investor returns",
        "bullets": [
          "ROI analysis",
          "Platform comparison",
          "Success rate trends over time"
        ]
      },
      "aiAssistant": {
        "title": "AI Assistant",
        "description": "Get instant answers to your crypto questions with our advanced AI assistant that provides personalized guidance and insights",
        "bullets": [
          "Instant crypto knowledge access",
          "Personalized investment strategies",
          "Handle all customer support needs"
        ]
      },
      "airdropScore": {
        "title": "Airdrop Score",
        "description": "Find legitimate airdrops with the highest potential value and lowest participation barriers through our verification system",
        "bullets": [
          "Airdrop legitimacy check",
          "Value assessment",
          "Participation requirements"
        ]
      },
      "gemScout": {
        "title": "Gem Scout",
        "description": "Discover early-stage projects with exceptional growth potential before they hit mainstream markets and major exchanges",
        "bullets": [
          "Early-stage detection",
          "Growth indicators",
          "Risk factor analysis"
        ]
      },
      "badges": {
        "betaTestingLive": "Beta Testing Live",
        "betaTestingSoon": "Beta Testing Soon",
        "comingSoon": "Coming Soon"
      }
    },
    "intelligence": {
      "title": "AI-Driven Intelligence for Smarter Crypto Decisions"
    },
    "faq": {
      "title": "Frequently Asked Questions",
      "questions": [
        {
          "question": "What makes CoinScout's analysis different from other platforms?",
          "answer": "CoinScout leverages AI-driven analytics to assess over 50 key factors across tokenomics, security, social sentiment, market performance, and project fundamentals. Unlike traditional platforms that primarily provide raw data, we transform complex metrics into clear, actionable insights—making it easier for investors at all levels to understand and make informed decisions."
        },
        {
          "question": "How accurate are CoinScout's investment recommendations?",
          "answer": "Our recommendations have achieved 500% greater accuracy than industry benchmarks in back-testing. We continuously refine our models with new data to maintain this edge."
        },
        {
          "question": "Is my portfolio data secure with CoinScout?",
          "answer": "Absolutely. We employ bank-level encryption and never store your private keys or wallet access. Your data is anonymized and used only to power your personal insights."
        },
        {
          "question": "Can I use CoinScout if I'm a beginner investor?",
          "answer": "Yes! CoinScout simplifies complex data into easy-to-understand scores for beginners and offers professionals a deep dive into advanced analytics with detailed documentation."
        }
      ]
    }
  },
  "nav:home": "Home",
  "nav:portfolio": "Portfolio",
  "nav:explore": "Explore",
  "nav:news": "News",
  "nav:learn": "Learn",
  "nav:profile": "Profile",
  "nav:settings": "Settings",
  "nav:logout": "Logout",
  "nav:login": "Login",
  "nav:register": "Register",
  "nav:trending": "Trending",
  "nav:favorites": "Favorites",
  "nav:watchlist": "Watchlist",

  // System messages
  "system": {
    "language": {
      "selector": {
        "title": "Language Settings",
        "label": "Select Language",
        "search": "Search languages...",
        "available": "Available Languages",
        "choose": "Choose language",
        "current": "Current language",
        "searching": "Searching..."
      }
    },
    "error": {
      "translation": "Translation error"
    },
    "data": {
      "loading": "Loading data",
      "empty": "No data available",
      "error": "Error loading data"
    }
  },

  // Authentication related strings
  "auth": {
    "email": "Email",
    "email.placeholder": "Enter your email",
    "email.description": "We'll never share your email with anyone else",
    "password": "Password",
    "password.placeholder": "Enter your password",
    "password.create": "Create a password",
    "password.confirm": "Confirm Password",
    "password.confirm.placeholder": "Confirm your password",
    "password.show": "Show password",
    "password.hide": "Hide password",
    "password.strength": "Password strength",
    "password.strength.weak": "Weak",
    "password.strength.good": "Good",
    "password.strength.strong": "Strong",
    "password.criteria.length": "At least 8 characters",
    "password.criteria.uppercase": "At least one uppercase letter",
    "password.criteria.lowercase": "At least one lowercase letter",
    "password.criteria.number": "At least one number",
    "password.criteria.special": "At least one special character",
    "password.reset.message": "Reset password functionality is coming soon",
    "captcha.protected": "This form is protected by reCAPTCHA",
    "terms.service": "Terms of Service",
    "terms.and": "and",
    "terms.privacy": "Privacy Policy",
    "forgotPassword": "Forgot Password?",
    "resetPassword": "Reset Password",
    "signin": "Sign In",
    "signin.loading": "Signing in...",
    "signin.securely": "Sign in securely",
    "signup": "Sign Up",
    "signout": "Sign Out",
    "accountCreated": "Account created successfully",
    "passwordResetSent": "Password reset email sent",
    "invalidCredentials": "Invalid email or password",
    "continueWith": "Continue with",
    "username": "Username",
    "username.placeholder": "Choose a username",
    "terms": {
      "agree": "I agree to the",
      "service": "Terms of Service",
      "and": "and",
      "privacy": "Privacy Policy"
    },
    "termsAccept": "By continuing, you agree to our Terms of Service and Privacy Policy",
    "remember": "Remember me for 30 days",
    "welcome.back": "Welcome back",
    "login": {
      "credential": {
        "prompt": "Enter your credentials to sign in to your account"
      },
      "success": {
        "title": "Success",
        "description": "You've successfully signed in"
      },
      "error": {
        "title": "Login error",
        "unknown": "Something went wrong. Please try again."
      }
    },
    "register": {
      "title": "Create an account",
      "create": "Create account",
      "creating": "Creating account...",
      "haveAccount": "Already have an account?",
      "success": "Registration successful",
      "success.detail": "Your account has been created successfully",
      "success.login": "Your account has been created, please login.",
      "success.email_verify": "Registration successful. Please check your email to verify your account.",
      "failed": "Registration failed",
      "failed.generic": "An error occurred during registration. Please try again.",
      "error": {
        "generic": "An error occurred. Please try again."
      },
      "description": "Create an account to get started"
    },
    "form": {
      "invalidFields": "Please fill in all required fields correctly"
    },
    "validation": {
      "email": "Please enter a valid email address",
      "password": {
        "length": "Password must be at least 6 characters"
      }
    }
  },

  // Flat format for auth (for backward compatibility)
  "auth:email": "Email",
  "auth:email.placeholder": "Enter your email",
  "auth:email.description": "We'll never share your email with anyone else",
  "auth:password": "Password",
  "auth:password.placeholder": "Enter your password",
  "auth:password.create": "Create a password",
  "auth:password.confirm": "Confirm Password",
  "auth:password.confirm.placeholder": "Confirm your password",
  "auth:password.show": "Show password",
  "auth:password.hide": "Hide password",
  "auth:password.strength": "Password strength",
  "auth:password.strength.weak": "Weak",
  "auth:password.strength.good": "Good",
  "auth:password.strength.strong": "Strong",
  "auth:password.reset.message": "Reset password functionality is coming soon",
  "auth:forgotPassword": "Forgot Password?",
  "auth:resetPassword": "Reset Password",
  "auth:signin": "Sign In",
  "auth:signin.loading": "Signing in...",
  "auth:signin.securely": "Sign in securely",
  "auth:signup": "Sign Up",
  "auth:signout": "Sign Out",
  "auth:accountCreated": "Account created successfully",
  "auth:passwordResetSent": "Password reset email sent",
  "auth:invalidCredentials": "Invalid email or password",
  "auth:continueWith": "Continue with",
  "auth:username": "Username",
  "auth:username.placeholder": "Choose a username",
  "auth:terms.agree": "I agree to the",
  "auth:terms.service": "Terms of Service",
  "auth:terms.and": "and",
  "auth:terms.privacy": "Privacy Policy",
  "auth:termsAccept": "By continuing, you agree to our Terms of Service and Privacy Policy",
  "auth:remember": "Remember me for 30 days",
  "auth:welcome.back": "Welcome back",
  "auth:login.credential.prompt": "Enter your credentials to sign in to your account",
  "auth:login.success.title": "Success",
  "auth:login.success.description": "You've successfully signed in",
  "auth:login.error.title": "Login error",
  "auth:login.error.unknown": "Something went wrong. Please try again.",
  "auth:register.title": "Create an account",
  "auth:register.create": "Create account",
  "auth:register.creating": "Creating account...",
  "auth:register.haveAccount": "Already have an account?",
  "auth:register.success": "Registration successful",
  "auth:register.success.detail": "Your account has been created successfully",
  "auth:register.success.login": "Your account has been created, please login.",
  "auth:register.success.email_verify": "Registration successful. Please check your email to verify your account.",
  "auth:register.failed": "Registration failed",
  "auth:register.failed.generic": "An error occurred during registration. Please try again.",
  "auth:register.error.generic": "An error occurred. Please try again.",
  "auth:register.description": "Create an account to get started",
  "auth:form.invalidFields": "Please fill in all required fields correctly",
  "auth:validation.email": "Please enter a valid email address",
  "auth:validation.email.required": "Email is required",
  "auth:validation.email.invalid": "Please enter a valid email address",
  "auth:validation.email.complete": "Please enter a complete email address",
  "auth:validation.password.required": "Password is required",
  "auth:validation.password.length": "Password must be at least 6 characters",
  "auth:validation.password.uppercase": "Password must contain at least one uppercase letter",
  "auth:validation.password.lowercase": "Password must contain at least one lowercase letter",
  "auth:validation.password.number": "Password must contain at least one number",
  "auth:validation.password.special": "Password must contain at least one special character",
  "auth:login.failed": "Login Failed",
  "auth:authentication.required": "Authentication Required",
  "auth:authentication.required.description": "Please log in to access this feature",
  "auth:authentication.signin": "Sign in",
  "auth:authentication.continueWithEmail": "Continue with email",
  "auth:authentication.goBack": "Go back",
  "auth:authentication.signInPrompt": "Sign in to access personalized features, save your preferences, and unlock the full capabilities of CoinScout.",
  "auth:backToHome": "Back to Home",

  // Coin list related strings
  "coinlist:allCoins": "All Coins",
  "coinlist:coinDetailDescription": "Click on any coin for detailed analysis",
  "coinlist:searchCoins": "Search coins...",
  "coinlist:searchBox.ariaLabel": "Search coins",
  "coinlist:aiPoweredTitle": "AI-Powered & Data-Driven Crypto Fundamental Ratings",
  "coinlist:comprehensiveAnalysis": "Comprehensive analysis and scoring of cryptocurrencies across",
  "coinlist:multipleMetrics": "multiple metrics",
  "coinlist:highlights": "Highlights",
  "coinlist:viewAll": "View All",
  "coinlist:currentPrice": "Current Price",
  "coinlist:marketCap": "Market Cap",
  "coinlist:rank": "Rank",
  "coinlist:filters.button": "Filters",
  "coinlist:filters.title": "Filter Options",
  "coinlist:filters.description": "Customize your view with advanced filtering options",
  "coinlist:columns.name": "Name",
  "coinlist:columns.tokenomics": "Tokenomics",
  "coinlist:columns.security": "Security",
  "coinlist:columns.social": "Social",
  "coinlist:columns.market": "Market",
  "coinlist:columns.insights": "Insights",
  "coinlist:columns.totalScore": "Total AI Score",
  "coinlist:columns.sevenDayChange": "7D Change",

  // Table header tooltips
  "coinlist:tooltips.name.title": "Coin Name & Symbol",
  "coinlist:tooltips.name.description": "The official name and symbol of the cryptocurrency as listed on exchanges.",
  "coinlist:tooltips.tokenomics.title": "Tokenomics Analysis",
  "coinlist:tooltips.tokenomics.description": "Measures token supply mechanisms, inflation risks, and vesting structures.",
  "coinlist:tooltips.security.title": "Security Analysis",
  "coinlist:tooltips.security.description": "Security audit results and risk assessment metrics.",
  "coinlist:tooltips.social.title": "Social Analysis",
  "coinlist:tooltips.social.description": "Social media presence, community engagement and sentiment analysis.",
  "coinlist:tooltips.market.title": "Market Performance Analysis",
  "coinlist:tooltips.market.description": "Measures trading volume, liquidity and overall market health.",
  "coinlist:tooltips.insights.title": "AI Insights Analysis",
  "coinlist:tooltips.insights.description": "AI-powered project insights, sentiment analysis and forecasting metrics.",
  "coinlist:tooltips.totalScore.title": "Total Score",
  "coinlist:tooltips.totalScore.description": "The overall rating calculated from all scoring metrics.",
  "coinlist:tooltips.sevenDayChange.title": "7 Day Change",
  "coinlist:tooltips.sevenDayChange.description": "Percentage price change over the last 7 days.",

  // Search tooltips
  "coinlist:search.inProgress": "Search in progress...",
  "coinlist:search.resultsUpdate": "Results will update automatically",
  "coinlist:search.clearSearch": "Clear search",

  // Highlight section titles
  "highlights:topGainers": "Top Movers",
  "highlights:newListings": "New Listings",
  "highlights:upcomingIDOs": "Upcoming IDOs",
  "highlights:gemCoins": "Gem Coins",
  "highlights:topAirdrops": "Top Airdrops",
  "highlights:score": "Score",

  // Coin detail related strings
  "coindetail:overview": "Overview",
  "coindetail:fundamentals": "Fundamentals",
  "coindetail:technicals": "Technicals",
  "coindetail:news": "News",
  "coindetail:social": "Social",
  "coindetail:developers": "Developers",
  "coindetail:analysis": "Analysis",
  "coindetail:price": "Price",
  "coindetail:volume": "Volume",
  "coindetail:marketCap": "Market Cap",
  "coindetail:circulatingSupply": "Circulating Supply",
  "coindetail:totalSupply": "Total Supply",
  "coindetail:maxSupply": "Max Supply",
  "coindetail:allTimeHigh": "All Time High",
  "coindetail:allTimeLow": "All Time Low",
  "coindetail:pricePrediction": "Price Prediction",
  "coindetail:addToWatchlist": "Add to Watchlist",
  "coindetail:removeFromWatchlist": "Remove from Watchlist",

  // Portfolio related strings
  "portfolio:totalValue": "Total Value",
  "portfolio:recentActivity": "Recent Activity",
  "portfolio:performance": "Performance",
  "portfolio:holdings": "Holdings",
  "portfolio:addAsset": "Add Asset",
  "portfolio:editAsset": "Edit Asset",
  "portfolio:noAssets": "No assets in portfolio",
  "portfolio:24hChange": "24h Change",
  "portfolio:allocation": "Allocation",
  "portfolio:profit": "Profit/Loss",

  // Settings related strings
  "settings:appearance": "Appearance",
  "settings:language": "Language",
  "settings:notifications": "Notifications",
  "settings:security": "Security",
  "settings:preferences": "Preferences",
  "settings:theme": "Theme",
  "settings:lightMode": "Light Mode",
  "settings:darkMode": "Dark Mode",
  "settings:systemDefault": "System Default",

  // Sidebar related strings
  "sidebar:lock": "Lock",
  "sidebar:unlock": "Unlock",
  "sidebar:collapse": "Collapse",
  "sidebar:cryptoRating": "Crypto Rating",
  "sidebar:idoRating": "IDO Rating",
  "sidebar:compareCoins": "Compare Coins",
  "sidebar:recentListings": "Recent Listings",
  "sidebar:topMovers": "Top Movers",
  "sidebar:watchlist": "My Watchlist",
  "sidebar:aiPortfolio": "AI Portfolio Guide",
  "sidebar:portfolioAudit": "Portfolio Audit",
  "sidebar:launchpads": "Launchpads",
  "sidebar:airdrops": "Airdrop Center",
  "sidebar:aiAssistant": "AI Assistant",
  "sidebar:gemScout": "Gem Scout",
  "sidebar:soon": "SOON",

  // Error messages
  "error:somethingWentWrong": "Something went wrong",
  "error:tryAgain": "Please try again",
  "error:networkIssue": "Network connection issue",
  "error:dataFetch": "Could not fetch data",
  "error:timeOut": "Request timed out",
  "error:invalidInput": "Invalid input",
  "error:pageNotFound": "Page not found",

  // Number formatting
  "format:thousand": "K",
  "format:million": "M",
  "format:billion": "B",
  "format:trillion": "T",

  // Coin age related
  "coin:age": "Age:",

  // Score status related
  "score:excellent": "Excellent",
  "score:positive": "Positive",
  "score:average": "Average",
  "score:weak": "Weak",
  "score:critical": "Critical",

  // Upcoming IDO page related strings
  "upcoming": {
    "title": "Upcoming Token Sales",
    "subtitle": "Discover and evaluate new tokens before they launch",
    "filters": {
      "title": "Filters",
      "saleType": "Sale Type",
      "allTypes": "All Types",
      "launchpad": "Launchpad",
      "allLaunchpads": "All Launchpads",
      "category": "Category",
      "allCategories": "All Categories",
      "blockchain": "Blockchain",
      "allBlockchains": "All Blockchains",
      "investor": "Investor",
      "allInvestors": "All Investors",
      "projectScore": "Project Score",
      "listingDate": "Listing Date",
      "reset": "Reset Filters",
      "apply": "Apply Filters",
      "searchCategories": "Search categories...",
      "searchChains": "Search chains...",
      "selectDateRange": "Select date range",
      "last24Hours": "Last 24 Hours",
      "last7Days": "Last 7 Days",
      "last14Days": "Last 14 Days",
      "last30Days": "Last 30 Days",
      "last90Days": "Last 90 Days"
    },
    "table": {
      "name": "Name",
      "launchDate": "Launch Date",
      "initialCap": "Initial Cap",
      "totalRaised": "Total Raised",
      "score": "Score",
      "actions": "Actions"
    },
    "search": "Search upcoming token sales...",
    "noResults": "No upcoming token sales found",
    "loading": "Loading upcoming token sales...",
    "error": "Error loading upcoming token sales",
    "retryButton": "Retry",
    "tba": "TBA",
    "rank": "Rank #{number}",
    "saleType": "Sale Type",
    "totalAiScore": "Total AI Score",
    "points": "Points",
    "tokenomics": "Tokenomics",
    "security": "Security",
    "social": "Social",
    "market": "Market",
    "insights": "Insights"
  },

  // Upcoming token sale types explanations
  "saleType": {
    "IDO": "Initial DEX Offering - Token sale conducted on a decentralized exchange (DEX)",
    "IEO": "Initial Exchange Offering - Token sale hosted on a centralized cryptocurrency exchange",
    "ICO": "Initial Coin Offering - First fundraising stage where new projects offer tokens to early investors",
    "SHO": "Strong Holder Offering - Token sale giving priority to long-term token holders",
    "Seed": "Seed Round - Early private funding round before public sale",
    "IGO": "Initial Game Offering - Fundraising focused on blockchain gaming projects",
    "ISO": "Initial Stake Offering - Token distribution through staking mechanism"
  },

  // Listing date options
  "listingDate": {
    "24hours": "Last 24 Hours",
    "7days": "Last 7 Days",
    "14days": "Last 14 Days",
    "30days": "Last 30 Days",
    "90days": "Last 90 Days"
  },

  // Footer related strings
  "footer": {
    "description": "Advanced cryptocurrency analytics and portfolio management platform powered by artificial intelligence.",
    "bankGradeSecurity": "Bank-grade Security",
    "allRightsReserved": "All rights reserved",
    "categories": {
      "product": "Product",
      "learn": "Learn",
      "community": "Community",
      "legal": "Legal"
    },
    "links": {
      "privacyPolicy": "Privacy Policy",
      "termsOfService": "Terms of Service",
      "cookiePolicy": "Cookie Policy",
      "disclaimer": "Disclaimer",
      "advertisingPolicy": "Advertising Policy",
      "careers": "Careers",
      "soon": "soon"
    }
  },

  // Navigation related strings
  "navigation": {
    "searchPlaceholder": "Search for Coins & Upcoming Projects",
    "goToHomepage": "Go to homepage",
    "coinScoutAlt": "CoinScout",
    "Pricing": "Pricing",
    "Documentation": "Documentation",
    "pricing": "Pricing",
    "goToApp": "Go to App",
    "login": "Login",
    "signUp": "Sign Up",
    "profile": "Profile",
    "membershipManagement": "Membership Management",
    "feedback": "Feedback",
    "adminDashboard": "Admin Dashboard",
    "logout": "Logout",
    "membership": {
      "premium": "Premium",
      "pro": "Pro",
      "free": "Free"
    }
  },

  // Error related strings
  "error": {
    "criticalError": "Critical Error",
    "somethingWentWrong": "Something went wrong",
    "criticalErrorMessage": "A critical error has occurred. Please try refreshing the page or return to the home page.",
    "returnToHome": "Return to Home",
    "multipleErrorsDetected": "Multiple Errors Detected",
    "unexpectedError": "An unexpected error occurred",
    "refreshPage": "Refresh Page",
    "goToHome": "Go to Home",
    "clearErrorLogs": "Clear Error Logs",
    "anErrorOccurred": "An error occurred"
  },

  // Watchlist related strings
  "watchlist": {
    "empty": {
      "title": "Your Watchlist is empty",
      "description": "You don't have any watchlist yet. Create a watchlist to track your cryptocurrencies.",
      "createWatchlist": "Create Watchlist",
      "addCoins": "Add Coins"
    },
    "noWatchlistsAvailable": "No watchlists available",
    "createFirstWatchlistUpcoming": "Create your first watchlist for upcoming projects",
    "createFirstWatchlistCoins": "Create your first watchlist to organize your coins"
  },

  // Upcoming IDO Tour related strings
  "upcomingTour": {
    "welcome": {
      "title": "Welcome to Upcoming IDOs",
      "description": "Would you like a quick tour of the Upcoming IDOs features?",
      "info": "Learn how to filter upcoming token sales, understand the project details, and evaluate projects before they launch.",
      "dontShowAgain": "Don't show this again",
      "skipButton": "Skip for now",
      "startButton": "Start Tour"
    },
    "steps": {
      "overview": {
        "title": "Upcoming IDOs Overview",
        "description": "Welcome to the Upcoming IDOs page, where you can discover and evaluate new token launches before they go live.",
        "details": "Browse, filter, and analyze upcoming token sales across different blockchains and launchpads."
      },
      "filters": {
        "title": "Filter Options",
        "description": "Use these filters to find specific types of token sales that match your investment criteria.",
        "details": "Filter by sale type, launchpad, category, blockchain, or investors.",
        "action": "Try selecting a filter to see how it updates the list."
      },
      "search": {
        "title": "Search & Find",
        "description": "Quickly search for any upcoming token sale by name or symbol.",
        "details": "Results update in real-time as you type."
      },
      "projectInfo": {
        "title": "Project Information",
        "description": "Each row contains detailed information about an upcoming token sale.",
        "details": "Click on any row to see detailed analysis for that project.",
        "action": "Try hovering over a project to see more information."
      },
      "initialCap": {
        "title": "Initial Market Cap",
        "description": "The expected initial market capitalization of the token upon listing.",
        "details": "Calculated as token price × circulating supply at TGE."
      },
      "score": {
        "title": "CoinScout Score",
        "description": "Our proprietary score evaluates the overall quality and potential of the project.",
        "details": "Based on tokenomics, security, social media activity, and more."
      },
      "launchDate": {
        "title": "Launch Date",
        "description": "The scheduled date when the token will be available for trading.",
        "details": "Stay updated with upcoming launches to prepare your investment strategy."
      },
      "pagination": {
        "title": "Page Navigation",
        "description": "Navigate through the list of upcoming token sales.",
        "details": "Adjust the number of projects displayed per page."
      },
      "completion": {
        "title": "You're All Set!",
        "description": "You've completed the tour of the Upcoming IDOs page.",
        "details": "Start exploring and analyzing upcoming token sales to find your next investment opportunity.",
        "help": "You can restart this tour anytime by clicking on the Guided Tour button."
      }
    }
  },

  // Flat format for upcoming (for backward compatibility)
  "upcoming:title": "Upcoming Token Sales",
  "upcoming:subtitle": "Discover and evaluate new tokens before they launch",
  "upcoming:filters.title": "Filters",
  "upcoming:filters.saleType": "Sale Type",
  "upcoming:filters.allTypes": "All Types",
  "upcoming:filters.launchpad": "Launchpad",
  "upcoming:filters.allLaunchpads": "All Launchpads",
  "upcoming:filters.category": "Category",
  "upcoming:filters.allCategories": "All Categories",
  "upcoming:filters.blockchain": "Blockchain",
  "upcoming:filters.allBlockchains": "All Blockchains",
  "upcoming:filters.investor": "Investor",
  "upcoming:filters.allInvestors": "All Investors",
  "upcoming:filters.projectScore": "Project Score",
  "upcoming:filters.listingDate": "Listing Date",
  "upcoming:filters.reset": "Reset Filters",
  "upcoming:filters.apply": "Apply Filters",
  "upcoming:table.name": "Name",
  "upcoming:table.launchDate": "Launch Date",
  "upcoming:table.initialCap": "Initial Cap",
  "upcoming:table.totalRaised": "Total Raised",
  "upcoming:table.score": "Score",
  "upcoming:table.actions": "Actions",
  "upcoming:search": "Search upcoming token sales...",
  "upcoming:noResults": "No upcoming token sales found",
  "upcoming:loading": "Loading upcoming token sales...",
  "upcoming:error": "Error loading upcoming token sales",
  "upcoming:retryButton": "Retry",
  "upcoming:tba": "TBA",
  "upcoming:rank": "Rank #{number}",
  "upcoming:saleType": "Sale Type",
  "upcoming:totalAiScore": "Total AI Score",
  "upcoming:points": "Points",
  "upcoming:tokenomics": "Tokenomics",
  "upcoming:security": "Security",
  "upcoming:social": "Social",
  "upcoming:market": "Market",
  "upcoming:insights": "Insights",

  // Methodology section translations
  "methodology": {
    "whatAreWeScoring": "What Are We Scoring",
    "whyIsThisImportant": "Why Is This Important",
    "scoringLevels": "Scoring Levels"
  },

  // Profile section translations
  "profile": {
    "yourProfile": "Your Profile",
    "howOthersSeeYou": "This is how others will see you on the platform.",
    "verified": "Verified",
    "unverified": "Unverified",
    "memberSince": "Member since",
    "unknown": "Unknown",
    "status": "Status",
    "active": "Active",
    "plan": "Plan",
    "unknownPlan": "Unknown Plan",
    "planStatus": "Plan Status",
    "started": "Started",
    "expires": "Expires",
    "lastLogin": "Last login",
    "never": "Never",
    "profileCompletion": "Profile completion",
    "improveTip": "Add a bio and profile picture to improve your profile.",
    "signOut": "Sign Out",
    "areYouSure": "Are you sure?",
    "signOutConfirmation": "You'll be signed out from your account. You can sign back in at any time.",
    "cancel": "Cancel",
    "personalInformation": "Personal Information",
    "updateDescription": "Update your personal information and how your profile appears.",
    "username": "Username",
    "email": "Email",
    "emailPlaceholder": "<EMAIL>",
    "emailSent": "Email sent",
    "verificationEmailSent": "Verification email sent successfully. Please check your inbox.",
    "error": "Error",
    "emailSendError": "An error occurred while sending email. Please try again.",
    "resendVerification": "Resend verification email",
    "membershipTier": "Membership Tier",
    "pro": "Pro",
    "premium": "Premium",
    "free": "Free",
    "manageSubscription": "Manage your subscription in the",
    "membership": "Membership",
    "tab": "tab",
    "downloadData": "Download Your Data",
    "deleteAccount": "Delete Account",
    "absolutelySure": "Are you absolutely sure?",
    "deleteWarning": "This action cannot be undone. This will permanently delete your account and remove your data from our servers.",
    "title": "Profile",
    "description": "Manage your account settings and preferences.",
    "saveChanges": "Save Changes",
    "tabs": {
      "profile": "Profile",
      "membership": "Membership",
      "notifications": "Notifications"
    }
  }
};

export default en;