/**
 * Russian localization strings
 */
const ru = {
  // Common strings
  "common": {
    "loading": "Загрузка...",
    "error": "Ошибка",
    "retry": "Повторить",
    "save": "Сохранить",
    "cancel": "Отменить",
    "close": "Закрыть",
    "success": "Успешно",
    "viewMore": "Смотреть больше",
    "back": "Назад",
    "next": "Далее",
    "search": "Поиск",
    "searchCoinsAndTokens": "Поиск монет и предстоящих проектов",
    "searching": "Поиск...",
    "noResults": "Результатов не найдено",
    "coins": "Монеты",
    "upcomingIdos": "Предстоящие IDO",
  },

  // Nav related strings
  "nav": {
    "home": "Главная",
    "portfolio": "Портфолио",
    "explore": "Обзор",
    "news": "Новости",
    "learn": "Обучение",
    "profile": "Профиль",
    "settings": "Настройки",
    "logout": "Выйти",
    "login": "Войти",
    "register": "Регистрация",
    "trending": "В тренде",
    "favorites": "Избранное",
    "watchlist": "Список наблюдения",
  },

  // System related strings
  "system": {
    "choose": "Выбрать язык",
    "current": "Текущий язык",
    "searching": "Поиск...",
  },

  // Error related strings
  "error": {
    "somethingWentWrong": "Что-то пошло не так",
    "tryAgain": "Пожалуйста, попробуйте снова",
    "networkIssue": "Проблема с сетевым подключением",
    "dataFetch": "Не удалось получить данные",
    "timeOut": "Превышено время ожидания",
    "invalidInput": "Неверный ввод",
    "pageNotFound": "Страница не найдена",
    "title": "[MISSING] Login error",
    "unknown": "[MISSING] Something went wrong. Please try again.",
  },

  // Data related strings
  "data": {
    "loading": "Загрузка данных",
    "empty": "Нет доступных данных",
    "error": "Ошибка загрузки данных",
  },

  // Auth related strings
  "auth": {
    "email": "Электронная почта",
    "emailPlaceholder": "Введите вашу электронную почту",
    "emailDescription": "Ваша электронная почта никогда не будет передана третьим лицам",
    "password": "Пароль",
    "passwordPlaceholder": "Введите ваш пароль",
    "passwordCreate": "Создать пароль",
    "passwordConfirm": "Подтвердите пароль",
    "passwordConfirmPlaceholder": "Повторно введите пароль",
    "passwordShow": "Показать пароль",
    "passwordHide": "Скрыть пароль",
    "passwordStrength": "Надежность пароля",
    "passwordStrengthWeak": "Слабый",
    "passwordStrengthGood": "Хороший",
    "passwordStrengthStrong": "Надежный",
    "passwordResetMessage": "Функция сброса пароля будет доступна в ближайшее время",
    "forgotPassword": "Забыли пароль?",
    "resetPassword": "Сбросить пароль",
    "signin": "Войти",
    "signinLoading": "Вход...",
    "signinSecurely": "Безопасный вход",
    "signup": "Регистрация",
    "signout": "Выйти",
    "accountCreated": "Аккаунт успешно создан",
    "passwordResetSent": "Отправлено письмо для сброса пароля",
    "invalidCredentials": "Неверная электронная почта или пароль",
    "continueWith": "Продолжить с",
    "username": "Имя пользователя",
    "usernamePlaceholder": "Выберите имя пользователя",
    "agree": "Я согласен с",
    "service": "Условиями использования",
    "and": "и",
    "privacy": "Политикой конфиденциальности",
    "email.placeholder": "Введите вашу электронную почту",
    "email.description": "Ваша электронная почта никогда не будет передана третьим лицам",
    "password.placeholder": "Введите ваш пароль",
    "password.create": "Создать пароль",
    "password.confirm": "Подтвердите пароль",
    "password.confirm.placeholder": "Повторно введите пароль",
    "password.show": "Показать пароль",
    "password.hide": "Скрыть пароль",
    "password.strength": "Надежность пароля",
    "password.strength.weak": "Слабый",
    "password.strength.good": "Хороший",
    "password.strength.strong": "Надежный",
    "password.reset.message": "Функция сброса пароля будет доступна в ближайшее время",
    "signin.loading": "Вход...",
    "signin.securely": "Безопасный вход",
    "username.placeholder": "Выберите имя пользователя",
  },

  // Login related strings
  "login": {
    "prompt": "Введите ваши данные для входа",
  },

  // Success related strings
  "success": {
    "title": "Успешно",
    "description": "Вход выполнен успешно",
  },

  // Register related strings
  "register": {
    "title": "Создать аккаунт",
    "create": "Создать аккаунт",
    "creating": "Создание аккаунта...",
    "haveAccount": "Уже есть аккаунт?",
    "success": "Регистрация успешна",
    "successDetail": "Аккаунт успешно создан",
    "successLogin": "Аккаунт создан. Пожалуйста, войдите.",
    "failed": "Ошибка регистрации",
    "failedGeneric": "Произошла ошибка при регистрации. Пожалуйста, попробуйте снова.",
    "generic": "Произошла ошибка. Пожалуйста, попробуйте снова.",
    "success.detail": "[MISSING] Your account has been created successfully",
    "success.login": "[MISSING] Your account has been created, please login.",
    "failed.generic": "[MISSING] An error occurred during registration. Please try again.",
  },

  // Form related strings
  "form": {
    "invalidFields": "Пожалуйста, заполните все обязательные поля корректно",
  },

  // Validation related strings
  "validation": {
    "email": "Пожалуйста, введите корректный адрес электронной почты",
    "length": "Пароль должен содержать не менее 6 символов",
  },

  // Coinlist related strings
  "coinlist": {
    "allCoins": "Все монеты",
    "coinDetailDescription": "Нажмите на любую монету для подробного анализа",
    "searchCoins": "Поиск монет...",
    "ariaLabel": "Поиск монет",
  },

  // Filters related strings
  "filters": {
    "button": "Фильтры",
    "title": "Параметры фильтрации",
    "description": "Настройте просмотр с помощью расширенных параметров фильтрации",
    "sortBy": "Сортировать по",
    "reset": "Сбросить фильтры",
  },

  // Columns related strings
  "columns": {
    "name": "Имя",
    "tokenomics": "Токеномика",
    "security": "Безопасность",
    "social": "Социальное",
    "market": "Рынок",
    "insights": "Аналитика",
    "totalScore": "Общая оценка ИИ",
    "sevenDayChange": "Изменение за 7Д",
    "price": "Цена",
  },

  // Highlights related strings
  "highlights": {
    "topGainers": "Монеты с Максимальным Ростом",
    "newListings": "Новые Листинги",
    "upcomingIDOs": "Предстоящие IDO",
    "gemCoins": "Перспективные Монеты",
    "topAirdrops": "Лучшие Airdrop",
    "score": "Оценка",
  },

  // Coindetail related strings
  "coindetail": {
    "overview": "Обзор",
    "fundamentals": "Фундаментальные показатели",
    "technicals": "Технический анализ",
    "news": "Новости",
    "social": "Социальное",
    "developers": "Разработчики",
    "analysis": "Анализ",
    "price": "Цена",
    "volume": "Объём",
    "marketCap": "Капитализация",
    "circulatingSupply": "В обращении",
    "totalSupply": "Общее предложение",
    "maxSupply": "Максимальное предложение",
    "allTimeHigh": "Исторический максимум",
    "allTimeLow": "Исторический минимум",
    "pricePrediction": "Прогноз цены",
    "addToWatchlist": "Добавить в список наблюдения",
    "removeFromWatchlist": "Удалить из списка наблюдения",
  },

  // Portfolio related strings
  "portfolio": {
    "totalValue": "Общая стоимость",
    "recentActivity": "Недавняя активность",
    "performance": "Производительность",
    "holdings": "Активы",
    "addAsset": "Добавить актив",
    "editAsset": "Изменить актив",
    "noAssets": "Нет активов в портфолио",
    "24hChange": "Изменение за 24ч",
    "allocation": "Распределение",
    "profit": "Прибыль/Убыток",
  },

  // Settings related strings
  "settings": {
    "appearance": "Внешний вид",
    "language": "Язык",
    "notifications": "Уведомления",
    "security": "Безопасность",
    "preferences": "Предпочтения",
    "theme": "Тема",
    "lightMode": "Светлый режим",
    "darkMode": "Тёмный режим",
    "systemDefault": "Системный режим",
  },

  // Sidebar related strings
  "sidebar": {
    "lock": "Закрепить",
    "unlock": "Открепить",
    "collapse": "Свернуть",
    "cryptoRating": "Рейтинг криптовалют",
    "idoRating": "Рейтинг IDO",
    "compareCoins": "Сравнение монет",
    "recentListings": "Новые листинги",
    "topMovers": "Лидеры роста",
    "watchlist": "Мой список наблюдения",
    "aiPortfolio": "ИИ-гид по портфолио",
    "portfolioAudit": "Аудит портфолио",
    "launchpads": "Площадки запуска",
    "airdrops": "Центр airdrop",
    "aiAssistant": "ИИ-ассистент",
    "gemScout": "Поиск перспективных монет",
    "soon": "СКОРО",
  },

  // Format related strings
  "format": {
    "thousand": "тыс",
    "million": "млн",
    "billion": "млрд",
    "trillion": "трлн",
  },

  // Coin related strings
  "coin": {
    "age": "Возраст:",
  },

  // Score related strings
  "score": {
    "excellent": "Отлично",
    "positive": "Хорошо",
    "average": "Средне",
    "weak": "Слабо",
    "critical": "Критично",
  },

  // Flat format for common (for backward compatibility)
  "common:loading": "Загрузка...",
  "common:error": "Ошибка",
  "common:retry": "Повторить",
  "common:save": "Сохранить",
  "common:cancel": "Отменить",
  "common:close": "Закрыть",
  "common:success": "Успешно",
  "common:viewMore": "Смотреть больше",
  "common:back": "Назад",
  "common:next": "Далее",
  "common:search": "Поиск",
  "common:searchCoinsAndTokens": "Поиск монет и предстоящих проектов",
  "common:searching": "Поиск...",
  "common:noResults": "Результатов не найдено",
  "common:coins": "Монеты",
  "common:upcomingIdos": "Предстоящие IDO",

  // Flat format for nav (for backward compatibility)
  "nav:home": "Главная",
  "nav:portfolio": "Портфолио",
  "nav:explore": "Обзор",
  "nav:news": "Новости",
  "nav:learn": "Обучение",
  "nav:profile": "Профиль",
  "nav:settings": "Настройки",
  "nav:logout": "Выйти",
  "nav:login": "Войти",
  "nav:register": "Регистрация",
  "nav:trending": "В тренде",
  "nav:favorites": "Избранное",
  "nav:watchlist": "Список наблюдения",

  // Flat format for system (for backward compatibility)
  "system:language.selector": "Поиск...",
  "system:error.translation": "Ошибка перевода",
  "system:data.loading": "Загрузка данных",
  "system:data.empty": "Нет доступных данных",
  "system:data.error": "Ошибка загрузки данных",

  // Flat format for auth (for backward compatibility)
  "auth:email": "Электронная почта",
  "auth:email.placeholder": "Введите вашу электронную почту",
  "auth:email.description": "Ваша электронная почта никогда не будет передана третьим лицам",
  "auth:password": "Пароль",
  "auth:password.placeholder": "Введите ваш пароль",
  "auth:password.create": "Создать пароль",
  "auth:password.confirm": "Подтвердите пароль",
  "auth:password.confirm.placeholder": "Повторно введите пароль",
  "auth:password.show": "Показать пароль",
  "auth:password.hide": "Скрыть пароль",
  "auth:password.strength": "Надежность пароля",
  "auth:password.strength.weak": "Слабый",
  "auth:password.strength.good": "Хороший",
  "auth:password.strength.strong": "Надежный",
  "auth:password.reset.message": "Функция сброса пароля будет доступна в ближайшее время",
  "auth:forgotPassword": "Забыли пароль?",
  "auth:resetPassword": "Сбросить пароль",
  "auth:signin": "Войти",
  "auth:signin.loading": "Вход...",
  "auth:signin.securely": "Безопасный вход",
  "auth:signup": "Регистрация",
  "auth:signout": "Выйти",
  "auth:accountCreated": "Аккаунт успешно создан",
  "auth:passwordResetSent": "Отправлено письмо для сброса пароля",
  "auth:invalidCredentials": "Неверная электронная почта или пароль",
  "auth:continueWith": "Продолжить с",
  "auth:username": "Имя пользователя",
  "auth:username.placeholder": "Выберите имя пользователя",
  "auth:terms.agree": "Я согласен с",
  "auth:terms.service": "Условиями использования",
  "auth:terms.and": "и",
  "auth:terms.privacy": "Политикой конфиденциальности",
  "auth:termsAccept": "Продолжая, вы соглашаетесь с нашими Условиями использования и Политикой конфиденциальности",
  "auth:remember": "Запомнить меня на 30 дней",
  "auth:welcome.back": "С возвращением",
  "auth:login.credential.prompt": "Введите ваши данные для входа",
  "auth:login.success.title": "Успешно",
  "auth:login.success.description": "Вход выполнен успешно",
  "auth:login.error.title": "Ошибка входа",
  "auth:login.error.unknown": "Что-то пошло не так. Пожалуйста, попробуйте снова.",
  "auth:register.title": "Создать аккаунт",
  "auth:register.create": "Создать аккаунт",
  "auth:register.creating": "Создание аккаунта...",
  "auth:register.haveAccount": "Уже есть аккаунт?",
  "auth:register.success": "Регистрация успешна",
  "auth:register.success.detail": "Аккаунт успешно создан",
  "auth:register.success.login": "Аккаунт создан. Пожалуйста, войдите.",
  "auth:register.success.email_verify": "Регистрация успешна. Пожалуйста, проверьте свою электронную почту для активации аккаунта.",
  "auth:register.failed": "Ошибка регистрации",
  "auth:register.failed.generic": "Произошла ошибка при регистрации. Пожалуйста, попробуйте снова.",
  "auth:register.error.generic": "Произошла ошибка. Пожалуйста, попробуйте снова.",
  "auth:register.description": "Создайте аккаунт, чтобы начать",
  "auth:form.invalidFields": "Пожалуйста, заполните все обязательные поля корректно",
  "auth:validation.email": "Пожалуйста, введите корректный адрес электронной почты",
  "auth:validation.password.length": "Пароль должен содержать не менее 6 символов",

  // Flat format for coinlist (for backward compatibility)
  "coinlist:allCoins": "Все монеты",
  "coinlist:coinDetailDescription": "Нажмите на любую монету для подробного анализа",
  "coinlist:searchCoins": "Поиск монет...",
  "coinlist:searchBox.ariaLabel": "Поиск монет",
  "coinlist:aiPoweredTitle": "Фундаментальные рейтинги криптовалют на основе ИИ и данных",
  "coinlist:comprehensiveAnalysis": "Комплексный анализ и оценка криптовалют по",
  "coinlist:multipleMetrics": "множеству показателей",
  "coinlist:highlights": "Основные моменты",
  "coinlist:viewAll": "Смотреть все",
  "coinlist:currentPrice": "Текущая цена",
  "coinlist:marketCap": "Капитализация",
  "coinlist:rank": "Ранг",
  "coinlist:filters.button": "Фильтры",
  "coinlist:filters.title": "Параметры фильтрации",
  "coinlist:filters.description": "Настройте просмотр с помощью расширенных параметров фильтрации",
  "coinlist:filters.sortBy": "Сортировать по",
  "coinlist:filters.reset": "Сбросить фильтры",
  "coinlist:columns.name": "Имя",
  "coinlist:columns.tokenomics": "Токеномика",
  "coinlist:columns.security": "Безопасность",
  "coinlist:columns.social": "Социальное",
  "coinlist:columns.market": "Рынок",
  "coinlist:columns.insights": "Аналитика",
  "coinlist:columns.totalScore": "Общая оценка ИИ",
  "coinlist:columns.sevenDayChange": "Изменение за 7Д",
  "coinlist:columns.price": "Цена",

  // Flat format for highlights (for backward compatibility)
  "highlights:topGainers": "Монеты с Максимальным Ростом",
  "highlights:newListings": "Новые Листинги",
  "highlights:upcomingIDOs": "Предстоящие IDO",
  "highlights:gemCoins": "Перспективные Монеты",
  "highlights:topAirdrops": "Лучшие Airdrop",
  "highlights:score": "Оценка",

  // Flat format for coindetail (for backward compatibility)
  "coindetail:overview": "Обзор",
  "coindetail:fundamentals": "Фундаментальные показатели",
  "coindetail:technicals": "Технический анализ",
  "coindetail:news": "Новости",
  "coindetail:social": "Социальное",
  "coindetail:developers": "Разработчики",
  "coindetail:analysis": "Анализ",
  "coindetail:price": "Цена",
  "coindetail:volume": "Объём",
  "coindetail:marketCap": "Капитализация",
  "coindetail:circulatingSupply": "В обращении",
  "coindetail:totalSupply": "Общее предложение",
  "coindetail:maxSupply": "Максимальное предложение",
  "coindetail:allTimeHigh": "Исторический максимум",
  "coindetail:allTimeLow": "Исторический минимум",
  "coindetail:pricePrediction": "Прогноз цены",
  "coindetail:addToWatchlist": "Добавить в список наблюдения",
  "coindetail:removeFromWatchlist": "Удалить из списка наблюдения",

  // Flat format for portfolio (for backward compatibility)
  "portfolio:totalValue": "Общая стоимость",
  "portfolio:recentActivity": "Недавняя активность",
  "portfolio:performance": "Производительность",
  "portfolio:holdings": "Активы",
  "portfolio:addAsset": "Добавить актив",
  "portfolio:editAsset": "Изменить актив",
  "portfolio:noAssets": "Нет активов в портфолио",
  "portfolio:24hChange": "Изменение за 24ч",
  "portfolio:allocation": "Распределение",
  "portfolio:profit": "Прибыль/Убыток",

  // Flat format for settings (for backward compatibility)
  "settings:appearance": "Внешний вид",
  "settings:language": "Язык",
  "settings:notifications": "Уведомления",
  "settings:security": "Безопасность",
  "settings:preferences": "Предпочтения",
  "settings:theme": "Тема",
  "settings:lightMode": "Светлый режим",
  "settings:darkMode": "Тёмный режим",
  "settings:systemDefault": "Системный режим",

  // Flat format for sidebar (for backward compatibility)
  "sidebar:lock": "Закрепить",
  "sidebar:unlock": "Открепить",
  "sidebar:collapse": "Свернуть",
  "sidebar:cryptoRating": "Рейтинг криптовалют",
  "sidebar:idoRating": "Рейтинг IDO",
  "sidebar:compareCoins": "Сравнение монет",
  "sidebar:recentListings": "Новые листинги",
  "sidebar:topMovers": "Лидеры роста",
  "sidebar:watchlist": "Мой список наблюдения",
  "sidebar:aiPortfolio": "ИИ-гид по портфолио",
  "sidebar:portfolioAudit": "Аудит портфолио",
  "sidebar:launchpads": "Площадки запуска",
  "sidebar:airdrops": "Центр airdrop",
  "sidebar:aiAssistant": "ИИ-ассистент",
  "sidebar:gemScout": "Поиск перспективных монет",
  "sidebar:soon": "СКОРО",

  // Flat format for error (for backward compatibility)
  "error:somethingWentWrong": "Что-то пошло не так",
  "error:tryAgain": "Пожалуйста, попробуйте снова",
  "error:networkIssue": "Проблема с сетевым подключением",
  "error:dataFetch": "Не удалось получить данные",
  "error:timeOut": "Превышено время ожидания",
  "error:invalidInput": "Неверный ввод",
  "error:pageNotFound": "Страница не найдена",

  // Flat format for format (for backward compatibility)
  "format:thousand": "тыс",
  "format:million": "млн",
  "format:billion": "млрд",
  "format:trillion": "трлн",

  // Flat format for coin (for backward compatibility)
  "coin:age": "Возраст:",

  // Flat format for score (for backward compatibility)
  "score:excellent": "Отлично",
  "score:positive": "Хорошо",
  "score:average": "Средне",
  "score:weak": "Слабо",
  "score:critical": "Критично",

  // Footer related strings
  "footer": {
    "description": "Продвинутая платформа анализа криптовалют и управления портфелем на основе искусственного интеллекта.",
    "bankGradeSecurity": "Безопасность Банковского Уровня",
    "allRightsReserved": "Все права защищены",
    "categories": {
      "product": "Продукт",
      "learn": "Обучение",
      "community": "Сообщество",
      "legal": "Правовая информация"
    },
    "links": {
      "privacyPolicy": "Политика Конфиденциальности",
      "termsOfService": "Условия Использования",
      "cookiePolicy": "Политика Cookies",
      "disclaimer": "Отказ от Ответственности",
      "advertisingPolicy": "Рекламная Политика",
      "careers": "Карьера",
      "soon": "скоро"
    }
  },

  // Navigation related strings
  "navigation": {
    "searchPlaceholder": "Поиск Монет и Предстоящих Проектов",
    "goToHomepage": "Перейти на главную",
    "coinScoutAlt": "CoinScout",
    "pricing": "Цены",
    "goToApp": "Перейти в Приложение",
    "login": "Войти",
    "signUp": "Регистрация",
    "profile": "Профиль",
    "membershipManagement": "Управление Членством",
    "feedback": "Обратная Связь",
    "adminDashboard": "Панель Администратора",
    "logout": "Выйти",
    "membership": {
      "premium": "Премиум",
      "pro": "Про",
      "free": "Бесплатно"
    }
  },

  // Error related strings
  "error": {
    "criticalError": "Критическая Ошибка",
    "somethingWentWrong": "Что-то пошло не так",
    "criticalErrorMessage": "Произошла критическая ошибка. Пожалуйста, обновите страницу или вернитесь на главную.",
    "returnToHome": "Вернуться на Главную",
    "multipleErrorsDetected": "Обнаружено Несколько Ошибок",
    "unexpectedError": "Произошла неожиданная ошибка",
    "refreshPage": "Обновить Страницу",
    "goToHome": "Перейти на Главную",
    "clearErrorLogs": "Очистить Журналы Ошибок",
    "anErrorOccurred": "Произошла ошибка"
  },

  // Watchlist related strings
  "watchlist": {
    "empty": {
      "title": "Ваш список наблюдения пуст",
      "description": "У вас пока нет списков наблюдения. Создайте список наблюдения для отслеживания ваших криптовалют.",
      "createWatchlist": "Создать Список Наблюдения",
      "addCoins": "Добавить Монеты"
    },
    "noWatchlistsAvailable": "Нет доступных списков наблюдения",
    "createFirstWatchlistUpcoming": "Создайте ваш первый список наблюдения для предстоящих проектов",
    "createFirstWatchlistCoins": "Создайте ваш первый список наблюдения для организации ваших монет"
  },

  // Methodology section translations
  "methodology": {
    "whatAreWeScoring": "Что Мы Оцениваем",
    "whyIsThisImportant": "Почему Это Важно",
    "scoringLevels": "Уровни Оценки"
  },

  // Profile section translations
  "profile": {
    "yourProfile": "Ваш Профиль",
    "howOthersSeeYou": "Так другие пользователи будут видеть вас на платформе.",
    "verified": "Верифицирован",
    "unverified": "Не верифицирован",
    "memberSince": "Участник с",
    "unknown": "Неизвестно",
    "status": "Статус",
    "active": "Активный",
    "plan": "План",
    "unknownPlan": "Неизвестный План",
    "planStatus": "Статус Плана",
    "started": "Начат",
    "expires": "Истекает",
    "lastLogin": "Последний вход",
    "never": "Никогда"
  },

  // Auth validation messages
  "auth:validation.email": "Пожалуйста, введите действительный адрес электронной почты",
  "auth:validation.email.required": "Электронная почта обязательна",
  "auth:validation.email.invalid": "Пожалуйста, введите действительный адрес электронной почты",
  "auth:validation.email.complete": "Пожалуйста, введите полный адрес электронной почты",
  "auth:validation.password.required": "Пароль обязателен",
  "auth:validation.password.length": "Пароль должен содержать не менее 6 символов",
  "auth:validation.password.uppercase": "Пароль должен содержать хотя бы одну заглавную букву",
  "auth:validation.password.lowercase": "Пароль должен содержать хотя бы одну строчную букву",
  "auth:validation.password.number": "Пароль должен содержать хотя бы одну цифру",
  "auth:validation.password.special": "Пароль должен содержать хотя бы один специальный символ",
  "auth:login.failed": "Ошибка входа",
  "auth:authentication.required": "Требуется аутентификация",
  "auth:authentication.required.description": "Пожалуйста, войдите в систему для доступа к этой функции",
  "auth:authentication.signin": "Войти",
  "auth:authentication.continueWithEmail": "Продолжить с электронной почтой",
  "auth:authentication.goBack": "Назад",
  "auth:authentication.signInPrompt": "Войдите, чтобы получить доступ к персонализированным функциям, сохранить свои предпочтения и разблокировать все возможности CoinScout.",
  "auth:backToHome": "Вернуться на главную",

  // Table header tooltips
  "coinlist:tooltips.name.title": "Название и символ монеты",
  "coinlist:tooltips.name.description": "Официальное название и символ криптовалюты, как указано на биржах.",
  "coinlist:tooltips.tokenomics.title": "Анализ токеномики",
  "coinlist:tooltips.tokenomics.description": "Измеряет механизмы предложения токенов, риски инфляции и структуры вестинга.",
  "coinlist:tooltips.security.title": "Анализ безопасности",
  "coinlist:tooltips.security.description": "Результаты аудита безопасности и метрики оценки рисков.",
  "coinlist:tooltips.social.title": "Социальный анализ",
  "coinlist:tooltips.social.description": "Присутствие в социальных сетях, вовлеченность сообщества и анализ настроений.",
  "coinlist:tooltips.market.title": "Анализ рыночной производительности",
  "coinlist:tooltips.market.description": "Измеряет объем торгов, ликвидность и общее состояние рынка.",
  "coinlist:tooltips.insights.title": "Анализ ИИ-инсайтов",
  "coinlist:tooltips.insights.description": "ИИ-инсайты проекта, анализ настроений и метрики прогнозирования.",
  "coinlist:tooltips.totalScore.title": "Общий балл",
  "coinlist:tooltips.totalScore.description": "Общий рейтинг, рассчитанный на основе всех метрик оценки.",
  "coinlist:tooltips.sevenDayChange.title": "Изменение за 7 дней",
  "coinlist:tooltips.sevenDayChange.description": "Процентное изменение цены за последние 7 дней.",

  // Search tooltips
  "coinlist:search.inProgress": "Поиск в процессе...",
  "coinlist:search.resultsUpdate": "Результаты будут обновляться автоматически",
  "coinlist:search.clearSearch": "Очистить поиск"
};

export default ru;