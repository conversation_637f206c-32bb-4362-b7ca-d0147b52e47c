/**
 * Turkish localization strings
 */
const tr = {
  // Navigation namespace
  navigation: {
    Pricing: "Fiyatlandırma",
    Documentation: "Dokümantasyon",
    pricing: "Fiyatlandırma",
    goToApp: "Uygulamaya Git",
    goToHomepage: "Ana Sayfaya Git",
    coinScoutAlt: "CoinScout Logosu"
  },

  // Footer namespace
  footer: {
    description: "Kapsamlı pazar içgörüleri ile yatırımcıların daha akıllı kararlar vermesine yardımcı olan AI destekli kripto para analiz platformu.",
    categories: {
      product: "Ürün",
      learn: "Öğren",
      community: "Topluluk",
      legal: "Yasal"
    },
    links: {
      soon: "Yakında"
    },
    allRightsReserved: "Tüm hakları saklıdır."
  },

  // System namespace
  system: {
    language: {
      selector: {
        title: "Dil Ayarları",
        label: "Dil Seç",
        search: "Dil ara...",
        available: "Mevcut Diller"
      }
    }
  },

  // Debugging ve geliştirme için log mesajları
  "logs": {
    "usingDirectAPIData": "🔄 Doğrudan API verisi kullanılıyor, veri uzunluğu:",
    "imageLoadError": "🖼️ Görsel yüklenirken hata:",
    "usingIdoWatchlists": "Yaklaşan projeler için IDO izleme listeleri kullanılıyor:",
    "rawAPIData": "🟡 API'den gelen ham veri:",
    "apiDataLength": "🟡 API'den gelen veri uzunluğu:",
    "filteredData": "🔍 Filtrelenmiş veri:",
    "filteredDataLength": "🔍 Filtrelenmiş veri uzunluğu:",
    "imageUrlChecks": "🖼️ Görsel URL kontrolleri:",
    "allProjectsFromAPI": "📌 API'den gelen tüm projeler:",
    "usingDirectAPI": "🟢 Doğrudan API verileri kullanılıyor",
    "rawAPIDataFirst5": "🔍 API'den gelen ham veriler (ilk 5 öğe):",
    "emptyAPIResult": "⚠️ API boş sonuç döndürdü, boş dizi kullanılıyor",
    "errorUsingEmptyArray": "❌ Hata nedeniyle boş dizi kullanılıyor",
    "totalAIScoreClicked": "Toplam AI Puanına tıklandı:",
    "filtersChanged": "Filtreler değiştirildi:",
    "upcomingIDOFirstUseEffect": "🔄 UpcomingIDO - İlk useEffect tetiklendi",
    "fetchInitialDataStarted": "🔄 fetchInitialData başladı",
    "fetchInitialDataCompleted": "🔄 fetchInitialData tamamlandı",
    "filterRunning": "🔍 Filtre çalışıyor, coin uzunluğu:",
    "paginatedData": "📄 Sayfalandırılmış veriler:",
    "first2Records": "📄 İlk 2 kayıt:",
    "paginatedDataImageFields": "📄 Sayfalanmış veri içinde görsel alanları:",
    "firstRecordDetails": "🧩 İlk kaydın detayları:",
    "firstRecordSocialScore": "📢 İlk kaydın social puanı değeri:",
    "clickedProjectInfo": "Tıklanan proje bilgileri:",
    "redirectingWithDirectID": "Doğrudan ID ile yönlendiriliyor:"
  },
  // Common strings
  "common": {
    "loading": "Yükleniyor...",
    "error": "Hata",
    "retry": "Tekrar Dene",
    "save": "Kaydet",
    "cancel": "İptal",
    "close": "Kapat",
    "success": "Başarılı",
    "viewMore": "Daha Fazla",
    "back": "Geri",
    "next": "İleri",
    "search": "Ara",
    "searchCoinsAndTokens": "Coin ve Proje Ara",
    "searching": "Aranıyor...",
    "noResults": "Sonuç bulunamadı",
    "coins": "Coinler",
    "upcomingIdos": "Yaklaşan IDO'lar"
  },

  // Nav related strings
  "nav": {
    "home": "Ana Sayfa",
    "portfolio": "Portföy",
    "explore": "Keşfet",
    "news": "Haberler",
    "learn": "Öğren",
    "profile": "Profil",
    "settings": "Ayarlar",
    "logout": "Çıkış Yap",
    "login": "Giriş Yap",
    "register": "Kayıt Ol",
    "trending": "Trendler",
    "favorites": "Favoriler",
    "watchlist": "İzleme Listesi",
    "pricing": "Fiyatlandırma",
    "documentation": "Dokümantasyon"
  },

  // Homepage content
  "homepage": {
    "hero": {
      "badge": "AI Destekli Kripto Analizi",
      "title": "Daha akıllı kripto yatırımları yapın",
      "titleHighlight": "AI içgörüleriyle",
      "description": "CoinScout, 40+ metrik üzerinden 3.000'den fazla kripto parayı analiz ederek başkalarından önce en iyi yatırım fırsatlarını belirlemenize yardımcı olur.",
      "ctaButton": "Ücretsiz Analiz Başlat",
      "stats": {
        "cryptocurrencies": "Kripto Para",
        "analysisMetrics": "Analiz Metrikleri",
        "moreAccuracy": "Daha Fazla Doğruluk"
      }
    },
    "features": {
      "title": "Üstün Yatırım Kararlarının Kilidini Açın",
      "subtitle": "Kapsamlı AI destekli araç paketimiz, kripto piyasasında güvenle gezinmenize yardımcı olur",
      "aiRating": {
        "title": "AI Geliştirilmiş Kripto Derecelendirmesi",
        "description": "Özel algoritmalar tokenomics, güvenlik, sosyal katılım ve piyasa faktörleri genelinde 50+ metriği değerlendirir",
        "bullets": [
          "Kapsamlı puanlama sistemi",
          "Derinlemesine güvenlik riski değerlendirmesi",
          "Gelişmiş tokenomics değerlendirmesi"
        ]
      },
      "idoRating": {
        "title": "IDO Derecelendirmesi",
        "description": "Gelecek vaat eden token tekliflerini lansmanından önce keşfedin ve değerlendirin, detaylı proje analizi ve puanlama ile",
        "bullets": [
          "Proje puanı ve risk analizi",
          "Launchpad takibi ve değerlendirmesi",
          "Fon toplama detayları ve tokenomics"
        ]
      },
      "compareCoins": {
        "title": "Coinleri Karşılaştır",
        "description": "Kriterlerinize göre en iyi yatırım fırsatlarını belirlemek için birden fazla kripto paranın yan yana analizi",
        "bullets": [
          "Çok faktörlü karşılaştırma",
          "Göreceli güç analizi",
          "Tarihsel performans takibi"
        ]
      },
      "portfolioGenerator": {
        "title": "Özel Portföy Oluşturucu",
        "description": "Risk toleransınız ve yatırım hedeflerinize göre çeşitlendirilmiş portföy oluşturmak için AI destekli öneriler",
        "bullets": [
          "AI destekli portföy özelleştirmesi",
          "Kişiselleştirilmiş risk toleransı ayarlamaları",
          "Otomatik portföy optimizasyonu"
        ]
      },
      "portfolioAnalysis": {
        "title": "AI Portföy Analizi",
        "description": "Otomatik risk analizi, çeşitlendirme puanı ve optimizasyon ipuçlarıyla mevcut portföyünüz hakkında AI destekli içgörüler edinin",
        "bullets": [
          "Portföy sağlık puanı analizi",
          "Risk maruziyeti değerlendirmesi",
          "Akıllı portföy dengeleme içgörüleri"
        ]
      },
      "launchpads": {
        "title": "En İyi Launchpadler",
        "description": "Performans metrikleri, başarı oranları ve yatırımcı getirileri ile yeni token teklifleri için en iyi platformları keşfedin",
        "bullets": [
          "ROI analizi",
          "Platform karşılaştırması",
          "Zaman içinde başarı oranı trendleri"
        ]
      },
      "aiAssistant": {
        "title": "AI Asistan",
        "description": "Kişiselleştirilmiş rehberlik ve içgörüler sağlayan gelişmiş AI asistanımızla kripto sorularınıza anında yanıtlar alın",
        "bullets": [
          "Anında kripto bilgi erişimi",
          "Kişiselleştirilmiş yatırım stratejileri",
          "Tüm müşteri destek ihtiyaçlarını karşılar"
        ]
      },
      "airdropScore": {
        "title": "Airdrop Puanı",
        "description": "Doğrulama sistemimiz aracılığıyla en yüksek potansiyel değer ve en düşük katılım engellerine sahip meşru airdropları bulun",
        "bullets": [
          "Airdrop meşruiyet kontrolü",
          "Değer değerlendirmesi",
          "Katılım gereksinimleri"
        ]
      },
      "gemScout": {
        "title": "Gem Scout",
        "description": "Ana akım piyasalara ve büyük borsalara ulaşmadan önce olağanüstü büyüme potansiyeli olan erken aşama projeleri keşfedin",
        "bullets": [
          "Erken aşama tespiti",
          "Büyüme göstergeleri",
          "Risk faktörü analizi"
        ]
      },
      "badges": {
        "betaTestingLive": "Beta Test Canlı",
        "betaTestingSoon": "Beta Test Yakında",
        "comingSoon": "Yakında"
      }
    },
    "intelligence": {
      "title": "Daha Akıllı Kripto Kararları için AI Destekli Zeka"
    },
    "faq": {
      "title": "Sıkça Sorulan Sorular",
      "questions": [
        {
          "question": "CoinScout'un analizini diğer platformlardan farklı kılan nedir?",
          "answer": "CoinScout, tokenomics, güvenlik, sosyal duyarlılık, piyasa performansı ve proje temelleri genelinde 50'den fazla anahtar faktörü değerlendirmek için AI destekli analitikleri kullanır. Öncelikle ham veri sağlayan geleneksel platformların aksine, karmaşık metrikleri net, uygulanabilir içgörülere dönüştürüyoruz—her seviyeden yatırımcının anlamasını ve bilinçli kararlar vermesini kolaylaştırıyoruz."
        },
        {
          "question": "CoinScout'un yatırım önerileri ne kadar doğru?",
          "answer": "Önerilerimiz geri testte endüstri kıyaslama noktalarından %500 daha yüksek doğruluk elde etti. Bu avantajı sürdürmek için modellerimizi sürekli olarak yeni verilerle iyileştiriyoruz."
        },
        {
          "question": "Portföy verilerim CoinScout ile güvenli mi?",
          "answer": "Kesinlikle. Banka seviyesinde şifreleme kullanıyoruz ve özel anahtarlarınızı veya cüzdan erişiminizi asla saklamıyoruz. Verileriniz anonimleştirilmiş ve yalnızca kişisel içgörülerinizi güçlendirmek için kullanılır."
        },
        {
          "question": "Acemi bir yatırımcıysam CoinScout'u kullanabilir miyim?",
          "answer": "Evet! CoinScout, yeni başlayanlar için karmaşık verileri anlaşılması kolay puanlara basitleştirir ve profesyonellere detaylı dokümantasyon ile gelişmiş analitikte derin dalış sunar."
        }
      ]
    }
  },

  // Navigation content
  "navigation": {
    "goToHomepage": "Ana Sayfaya Git",
    "coinScoutAlt": "CoinScout Logosu",
    "Pricing": "Fiyatlandırma",
    "Documentation": "Dokümantasyon",
    "pricing": "Fiyatlandırma",
    "goToApp": "Uygulamaya Git"
  },

  // Footer content
  "footer": {
    "description": "Kapsamlı pazar içgörüleri ile yatırımcıların daha akıllı kararlar vermesine yardımcı olan AI destekli kripto para analiz platformu.",
    "categories": {
      "product": "Ürün",
      "learn": "Öğren",
      "community": "Topluluk",
      "legal": "Yasal"
    },
    "links": {
      "soon": "Yakında"
    },
    "allRightsReserved": "Tüm hakları saklıdır."
  },

  // System content
  "system": {
    "language": {
      "selector": {
        "title": "Dil Ayarları",
        "label": "Dil Seç",
        "search": "Dil ara...",
        "available": "Mevcut Diller"
      }
    }
  },

  // System related strings

  // Auth related strings
  "auth": {
    "email": "E-posta",
    "email.placeholder": "E-posta adresinizi girin",
    "email.description": "E-posta adresinizi asla başkalarıyla paylaşmayız",
    "password": "Şifre",
    "password.placeholder": "Şifrenizi girin",
    "password.create": "Bir şifre oluşturun",
    "password.confirm": "Şifreyi Onayla",
    "password.confirm.placeholder": "Şifrenizi onaylayın",
    "password.show": "Şifreyi göster",
    "password.hide": "Şifreyi gizle",
    "password.strength": "Şifre gücü",
    "password.strength.weak": "Zayıf",
    "password.strength.good": "İyi",
    "password.strength.strong": "Güçlü",
    "password.criteria.length": "En az 8 karakter",
    "password.criteria.uppercase": "En az bir büyük harf",
    "password.criteria.lowercase": "En az bir küçük harf",
    "password.criteria.number": "En az bir rakam",
    "password.criteria.special": "En az bir özel karakter",
    "password.reset.message": "Şifre sıfırlama işlevi yakında gelecek",
    "captcha.protected": "Bu form reCAPTCHA ile korunmaktadır",
    "terms.service": "Kullanım Şartları",
    "terms.and": "ve",
    "terms.privacy": "Gizlilik Politikası",
    "forgotPassword": "Şifremi Unuttum?",
    "resetPassword": "Şifreyi Sıfırla",
    "signin": "Giriş Yap",
    "signin.loading": "Giriş yapılıyor...",
    "signin.securely": "Güvenli giriş yapın",
    "signup": "Kayıt Ol",
    "signout": "Çıkış Yap",
    "accountCreated": "Hesap başarıyla oluşturuldu",
    "passwordResetSent": "Şifre sıfırlama e-postası gönderildi",
    "invalidCredentials": "Geçersiz e-posta veya şifre",
    "continueWith": "Devam et:",
    "username": "Kullanıcı adı",
    "username.placeholder": "Bir kullanıcı adı seçin",
    "agree": "Kabul ediyorum",
    "service": "Hizmet Şartları",
    "and": "ve",
    "privacy": "Gizlilik Politikası",
  },

  // Login related strings
  "login": {
    "prompt": "Hesabınıza giriş yapmak için bilgilerinizi girin",
  },

  // Success related strings
  "success": {
    "title": "Başarılı",
    "description": "Başarıyla giriş yaptınız",
  },

  // Register related strings
  "register": {
    "title": "Hesap oluştur",
    "create": "Hesap oluştur",
    "creating": "Hesap oluşturuluyor...",
    "haveAccount": "Zaten hesabınız var mı?",
    "success": "Kayıt başarılı",
    "success.detail": "Hesabınız başarıyla oluşturuldu",
    "success.login": "Hesabınız oluşturuldu, lütfen giriş yapın.",
    "failed": "Kayıt başarısız",
    "failed.generic": "Kayıt sırasında bir hata oluştu. Lütfen tekrar deneyin.",
    "generic": "Bir hata oluştu. Lütfen tekrar deneyin.",
  },

  // Form related strings
  "form": {
    "invalidFields": "Lütfen tüm gerekli alanları doğru şekilde doldurun",
  },

  // Validation related strings
  "validation": {
    "email": "Lütfen geçerli bir e-posta adresi girin",
    "length": "Şifre en az 6 karakter olmalıdır",
  },

  // Flat format for common (for backward compatibility)
  "common:loading": "Yükleniyor...",
  "common:error": "Hata",
  "common:retry": "Tekrar Dene",
  "common:save": "Kaydet",
  "common:cancel": "İptal",
  "common:close": "Kapat",
  "common:success": "Başarılı",
  "common:viewMore": "Daha Fazla Gör",
  "common:back": "Geri",
  "common:next": "İleri",
  "common:search": "Ara",
  "common:searchCoinsAndTokens": "Coin ve token ara",
  "common:searching": "Aranıyor...",
  "common:noResults": "Sonuç bulunamadı",
  "common:coins": "Coinler",
  "common:upcomingIdos": "Yaklaşan IDO'lar",

  // Flat format for nav (for backward compatibility)
  "nav:home": "Ana Sayfa",
  "nav:portfolio": "Portföy",
  "nav:explore": "Keşfet",
  "nav:news": "Haberler",
  "nav:learn": "Öğren",
  "nav:profile": "Profil",
  "nav:settings": "Ayarlar",
  "nav:logout": "Çıkış Yap",
  "nav:login": "Giriş Yap",
  "nav:register": "Kayıt Ol",
  "nav:trending": "Trend",
  "nav:favorites": "Favoriler",
  "nav:watchlist": "İzleme Listesi",

  // Flat format for system (for backward compatibility)
  "system:language.selector": "Aranıyor...",
  "system:error.translation": "Çeviri hatası",
  "system:data.loading": "Veriler yükleniyor",
  "system:data.empty": "Veri bulunamadı",
  "system:data.error": "Veri yüklenirken hata oluştu",

  // Flat format for auth (for backward compatibility)
  "auth:email": "E-posta",
  "auth:email.placeholder": "E-posta adresinizi girin",
  "auth:email.description": "E-posta adresinizi asla başkalarıyla paylaşmayız",
  "auth:password": "Şifre",
  "auth:password.placeholder": "Şifrenizi girin",
  "auth:password.create": "Bir şifre oluşturun",
  "auth:password.confirm": "Şifreyi Onayla",
  "auth:password.confirm.placeholder": "Şifrenizi onaylayın",
  "auth:password.show": "Şifreyi göster",
  "auth:password.hide": "Şifreyi gizle",
  "auth:password.strength": "Şifre gücü",
  "auth:password.strength.weak": "Zayıf",
  "auth:password.strength.good": "İyi",
  "auth:password.strength.strong": "Güçlü",
  "auth:password.reset.message": "Şifre sıfırlama işlevi yakında gelecek",
  "auth:forgotPassword": "Şifremi Unuttum?",
  "auth:resetPassword": "Şifreyi Sıfırla",
  "auth:signin": "Giriş Yap",
  "auth:signin.loading": "Giriş yapılıyor...",
  "auth:signin.securely": "Güvenli giriş yapın",
  "auth:signup": "Kayıt Ol",
  "auth:signout": "Çıkış Yap",
  "auth:accountCreated": "Hesap başarıyla oluşturuldu",
  "auth:passwordResetSent": "Şifre sıfırlama e-postası gönderildi",
  "auth:invalidCredentials": "Geçersiz e-posta veya şifre",
  "auth:continueWith": "Devam et:",
  "auth:username": "Kullanıcı adı",
  "auth:username.placeholder": "Bir kullanıcı adı seçin",
  "auth:terms.agree": "Kabul ediyorum",
  "auth:terms.service": "Hizmet Şartları",
  "auth:terms.and": "ve",
  "auth:terms.privacy": "Gizlilik Politikası",
  "auth:termsAccept": "Devam ederek, Hizmet Şartlarımızı ve Gizlilik Politikamızı kabul etmiş olursunuz",
  "auth:remember": "Beni 30 gün hatırla",
  "auth:welcome.back": "Tekrar hoş geldiniz",
  "auth:login.credential.prompt": "Hesabınıza giriş yapmak için bilgilerinizi girin",
  "auth:login.success.title": "Başarılı",
  "auth:login.success.description": "Başarıyla giriş yaptınız",
  "auth:login.error.title": "Giriş hatası",
  "auth:login.error.unknown": "Bir şeyler yanlış gitti. Lütfen tekrar deneyin.",
  "auth:register.title": "Hesap oluştur",
  "auth:register.create": "Hesap oluştur",
  "auth:register.creating": "Hesap oluşturuluyor...",
  "auth:register.haveAccount": "Zaten hesabınız var mı?",
  "auth:register.success": "Kayıt başarılı",
  "auth:register.success.detail": "Hesabınız başarıyla oluşturuldu",
  "auth:register.success.login": "Hesabınız oluşturuldu, lütfen giriş yapın.",
  "auth:register.success.email_verify": "Kayıt işlemi başarılı. Lütfen hesabınızı doğrulamak için e-postanızı kontrol edin.",
  "auth:register.failed": "Kayıt başarısız",
  "auth:register.failed.generic": "Kayıt sırasında bir hata oluştu. Lütfen tekrar deneyin.",
  "auth:register.error.generic": "Bir hata oluştu. Lütfen tekrar deneyin.",
  "auth:register.description": "Başlamak için hesap oluşturun",
  "auth:form.invalidFields": "Lütfen tüm gerekli alanları doğru şekilde doldurun",
  "auth:validation.email": "Lütfen geçerli bir e-posta adresi girin",
  "auth:validation.email.required": "E-posta gereklidir",
  "auth:validation.email.invalid": "Lütfen geçerli bir e-posta adresi girin",
  "auth:validation.email.complete": "Lütfen tam bir e-posta adresi girin",
  "auth:validation.password.required": "Şifre gereklidir",
  "auth:validation.password.length": "Şifre en az 6 karakter olmalıdır",
  "auth:validation.password.uppercase": "Şifre en az bir büyük harf içermelidir",
  "auth:validation.password.lowercase": "Şifre en az bir küçük harf içermelidir",
  "auth:validation.password.number": "Şifre en az bir rakam içermelidir",
  "auth:validation.password.special": "Şifre en az bir özel karakter içermelidir",
  "auth:login.failed": "Giriş Başarısız",
  "auth:authentication.required": "Kimlik Doğrulama Gerekli",
  "auth:authentication.required.description": "Bu özelliğe erişmek için lütfen giriş yapın",
  "auth:authentication.signin": "Giriş yap",
  "auth:authentication.continueWithEmail": "E-posta ile devam et",
  "auth:authentication.goBack": "Geri dön",
  "auth:authentication.signInPrompt": "Kişiselleştirilmiş özelliklere erişmek, tercihlerinizi kaydetmek ve CoinScout'un tüm yeteneklerini açmak için giriş yapın.",
  "auth:backToHome": "Ana Sayfaya Dön",

  // Flat format for coinlist (for backward compatibility)
  "coinlist:allCoins": "Tüm Koinler",
  "coinlist:coinDetailDescription": "Detaylı analiz için herhangi bir koine tıklayın",
  "coinlist:searchCoins": "Koin ara...",
  "coinlist:searchBox.ariaLabel": "Koin ara",
  "coinlist:aiPoweredTitle": "Yapay Zeka Destekli ve Veri Odaklı Kripto Temel Derecelendirmeleri",
  "coinlist:comprehensiveAnalysis": "Kripto paraların çoklu kriterlere göre kapsamlı analizi ve puanlaması",
  "coinlist:multipleMetrics": " ",
  "coinlist:highlights": "Öne Çıkanlar",
  "coinlist:viewAll": "Tümünü Gör",
  "coinlist:currentPrice": "Güncel Fiyat",
  "coinlist:marketCap": "Piyasa Değeri",
  "coinlist:rank": "Sıralama",
  "coinlist:filters.button": "Filtreler",
  "coinlist:filters.title": "Filtre Seçenekleri",
  "coinlist:filters.description": "Gelişmiş filtreleme seçenekleriyle görünümünüzü özelleştirin",
  "coinlist:columns.name": "İsim",
  "coinlist:columns.tokenomics": "Tokenomiks",
  "coinlist:columns.security": "Güvenlik",
  "coinlist:columns.social": "Sosyal",
  "coinlist:columns.market": "Piyasa",
  "coinlist:columns.insights": "İçgörüler",
  "coinlist:columns.totalScore": "Toplam YZ Puanı",
  "coinlist:columns.sevenDayChange": "7G Değişim",

  // Table header tooltips
  "coinlist:tooltips.name.title": "Koin Adı ve Sembolü",
  "coinlist:tooltips.name.description": "Borsalarda listelenen kripto paranın resmi adı ve sembolü.",
  "coinlist:tooltips.tokenomics.title": "Tokenomiks Analizi",
  "coinlist:tooltips.tokenomics.description": "Token arz mekanizmalarını, enflasyon risklerini ve vesting yapılarını ölçer.",
  "coinlist:tooltips.security.title": "Güvenlik Analizi",
  "coinlist:tooltips.security.description": "Güvenlik denetimi sonuçları ve risk değerlendirme metrikleri.",
  "coinlist:tooltips.social.title": "Sosyal Analiz",
  "coinlist:tooltips.social.description": "Sosyal medya varlığı, topluluk katılımı ve duygu analizi.",
  "coinlist:tooltips.market.title": "Piyasa Performans Analizi",
  "coinlist:tooltips.market.description": "İşlem hacmini, likiditeyi ve genel piyasa sağlığını ölçer.",
  "coinlist:tooltips.insights.title": "YZ İçgörü Analizi",
  "coinlist:tooltips.insights.description": "YZ destekli proje içgörüleri, duygu analizi ve tahmin metrikleri.",
  "coinlist:tooltips.totalScore.title": "Toplam Puan",
  "coinlist:tooltips.totalScore.description": "Tüm puanlama metriklerinden hesaplanan genel değerlendirme.",
  "coinlist:tooltips.sevenDayChange.title": "7 Günlük Değişim",
  "coinlist:tooltips.sevenDayChange.description": "Son 7 gündeki yüzdelik fiyat değişimi.",

  // Search tooltips
  "coinlist:search.inProgress": "Arama devam ediyor...",
  "coinlist:search.resultsUpdate": "Sonuçlar otomatik olarak güncellenecek",
  "coinlist:search.clearSearch": "Aramayı temizle",

  // Flat format for highlights (for backward compatibility)
  "highlights:topGainers": "Yükselen Koinler",
  "highlights:newListings": "Yeni Listelenenler",
  "highlights:upcomingIDOs": "Yaklaşan IDO'lar",
  "highlights:gemCoins": "Gem Projeler",
  "highlights:topAirdrops": "En İyi Airdrop'lar",
  "highlights:score": "Puan",

  // Flat format for coindetail (for backward compatibility)
  "coindetail:overview": "Genel Bakış",
  "coindetail:fundamentals": "Temel Analiz",
  "coindetail:technicals": "Teknik Analiz",
  "coindetail:news": "Haberler",
  "coindetail:social": "Sosyal",
  "coindetail:developers": "Geliştiriciler",
  "coindetail:analysis": "Analiz",
  "coindetail:price": "Fiyat",
  "coindetail:volume": "Hacim",
  "coindetail:marketCap": "Piyasa Değeri",
  "coindetail:circulatingSupply": "Dolaşımdaki Arz",
  "coindetail:totalSupply": "Toplam Arz",
  "coindetail:maxSupply": "Maksimum Arz",
  "coindetail:allTimeHigh": "Tüm Zamanların En Yükseği",
  "coindetail:allTimeLow": "Tüm Zamanların En Düşüğü",
  "coindetail:pricePrediction": "Fiyat Tahmini",
  "coindetail:addToWatchlist": "İzleme Listesine Ekle",
  "coindetail:removeFromWatchlist": "İzleme Listesinden Çıkar",

  // Flat format for portfolio (for backward compatibility)
  "portfolio:totalValue": "Toplam Değer",
  "portfolio:recentActivity": "Son Aktivite",
  "portfolio:performance": "Performans",
  "portfolio:holdings": "Varlıklar",
  "portfolio:addAsset": "Varlık Ekle",
  "portfolio:editAsset": "Varlık Düzenle",
  "portfolio:noAssets": "Portföyde varlık yok",
  "portfolio:24hChange": "24s Değişim",
  "portfolio:allocation": "Dağılım",
  "portfolio:profit": "Kâr/Zarar",

  // Flat format for settings (for backward compatibility)
  "settings:appearance": "Görünüm",
  "settings:language": "Dil",
  "settings:notifications": "Bildirimler",
  "settings:security": "Güvenlik",
  "settings:preferences": "Tercihler",
  "settings:theme": "Tema",
  "settings:lightMode": "Açık Mod",
  "settings:darkMode": "Koyu Mod",
  "settings:systemDefault": "Sistem Varsayılanı",

  // Flat format for sidebar (for backward compatibility)
  "sidebar:lock": "Kilitle",
  "sidebar:unlock": "Kilidi Aç",
  "sidebar:collapse": "Daralt",
  "sidebar:cryptoRating": "Kripto Dereceleri",
  "sidebar:idoRating": "IDO Dereceleri",
  "sidebar:compareCoins": "Koin Karşılaştır",
  "sidebar:recentListings": "Yeni Listeler",
  "sidebar:topMovers": "En Çok Yükselenler",
  "sidebar:watchlist": "İzleme Listem",
  "sidebar:aiPortfolio": "AI Portföy Rehberi",
  "sidebar:portfolioAudit": "Portföy Denetimi",
  "sidebar:launchpads": "Launchpad'ler",
  "sidebar:airdrops": "Airdrop Merkezi",
  "sidebar:aiAssistant": "AI Asistan",
  "sidebar:gemScout": "Gem Scout",
  "sidebar:soon": "YAKINDA",

  // Flat format for error (for backward compatibility)
  "error:somethingWentWrong": "Bir şeyler yanlış gitti",
  "error:tryAgain": "Lütfen tekrar deneyin",
  "error:networkIssue": "Ağ bağlantı sorunu",
  "error:dataFetch": "Veriler alınamadı",
  "error:timeOut": "İstek zaman aşımına uğradı",
  "error:invalidInput": "Geçersiz giriş",
  "error:pageNotFound": "Sayfa bulunamadı",

  // Flat format for format (for backward compatibility)
  "format:thousand": "B",
  "format:million": "Mn",
  "format:billion": "Mr",
  "format:trillion": "Tr",

  // Flat format for coin (for backward compatibility)
  "coin:age": "Yaş:",

  // Flat format for score (for backward compatibility)
  "score:excellent": "Mükemmel",
  "score:positive": "Olumlu",
  "score:average": "Ortalama",
  "score:weak": "Zayıf",
  "score:critical": "Kritik",

  // Footer related strings
  "footer": {
    "description": "Yapay zeka destekli gelişmiş kripto para analizi ve portföy yönetimi platformu.",
    "bankGradeSecurity": "Banka Seviyesi Güvenlik",
    "allRightsReserved": "Tüm hakları saklıdır",
    "categories": {
      "product": "Ürün",
      "learn": "Öğren",
      "community": "Topluluk",
      "legal": "Yasal"
    },
    "links": {
      "privacyPolicy": "Gizlilik Politikası",
      "termsOfService": "Hizmet Şartları",
      "cookiePolicy": "Çerez Politikası",
      "disclaimer": "Sorumluluk Reddi",
      "advertisingPolicy": "Reklam Politikası",
      "careers": "Kariyer",
      "soon": "yakında"
    }
  },

  // Navigation related strings
  "navigation": {
    "searchPlaceholder": "Coin ve Proje Ara",
    "goToHomepage": "Ana sayfaya git",
    "coinScoutAlt": "CoinScout",
    "pricing": "Fiyatlandırma",
    "goToApp": "Uygulamaya Git",
    "login": "Giriş Yap",
    "signUp": "Kayıt Ol",
    "profile": "Profil",
    "membershipManagement": "Üyelik Yönetimi",
    "feedback": "Geri Bildirim",
    "adminDashboard": "Admin Paneli",
    "logout": "Çıkış Yap",
    "membership": {
      "premium": "Premium",
      "pro": "Pro",
      "free": "Ücretsiz"
    }
  },

  // Error related strings
  "error": {
    "criticalError": "Kritik Hata",
    "somethingWentWrong": "Bir şeyler yanlış gitti",
    "criticalErrorMessage": "Kritik bir hata oluştu. Lütfen sayfayı yenileyin veya ana sayfaya dönün.",
    "returnToHome": "Ana Sayfaya Dön",
    "multipleErrorsDetected": "Birden Fazla Hata Tespit Edildi",
    "unexpectedError": "Beklenmeyen bir hata oluştu",
    "refreshPage": "Sayfayı Yenile",
    "goToHome": "Ana Sayfaya Git",
    "clearErrorLogs": "Hata Kayıtlarını Temizle",
    "anErrorOccurred": "Bir hata oluştu"
  },

  // Watchlist related strings
  "watchlist": {
    "empty": {
      "title": "İzleme listeniz boş",
      "description": "Henüz hiç izleme listeniz yok. Kripto paralarınızı takip etmek için bir izleme listesi oluşturun.",
      "createWatchlist": "İzleme Listesi Oluştur",
      "addCoins": "Coin Ekle"
    },
    "noWatchlistsAvailable": "Kullanılabilir izleme listesi yok",
    "createFirstWatchlistUpcoming": "Yaklaşan projeler için ilk izleme listenizi oluşturun",
    "createFirstWatchlistCoins": "Coinlerinizi organize etmek için ilk izleme listenizi oluşturun"
  },

  // Upcoming IDO page related strings
  "upcoming": {
    "title": "Yaklaşan Token Satışları",
    "subtitle": "Piyasaya sürülmeden önce yeni tokenleri keşfedin ve değerlendirin",
    "filters": {
      "title": "Filtreler",
      "saleType": "Satış Tipi",
      "allTypes": "Tüm Tipler",
      "launchpad": "Launchpad",
      "allLaunchpads": "Tüm Launchpad'ler",
      "category": "Kategori",
      "allCategories": "Tüm Kategoriler",
      "blockchain": "Blockchain",
      "allBlockchains": "Tüm Blockchain'ler",
      "investor": "Yatırımcı",
      "allInvestors": "Tüm Yatırımcılar",
      "projectScore": "Proje Puanı",
      "listingDate": "Listelenme Tarihi",
      "reset": "Filtreleri Sıfırla",
      "apply": "Filtreleri Uygula",
      "searchCategories": "Kategorileri ara...",
      "searchChains": "Blockchain'leri ara...",
      "selectDateRange": "Tarih aralığı seç",
      "last24Hours": "Son 24 Saat",
      "last7Days": "Son 7 Gün",
      "last14Days": "Son 14 Gün",
      "last30Days": "Son 30 Gün",
      "last90Days": "Son 90 Gün"
    },
    "table": {
      "name": "İsim",
      "launchDate": "Başlangıç Tarihi",
      "initialCap": "Başlangıç Piyasa Değeri",
      "totalRaised": "Toplam Fon",
      "score": "Puan",
      "actions": "İşlemler"
    },
    "search": "Yaklaşan token satışlarını ara...",
    "noResults": "Yaklaşan token satışı bulunamadı",
    "loading": "Yaklaşan token satışları yükleniyor...",
    "error": "Yaklaşan token satışları yüklenirken hata oluştu",
    "retryButton": "Tekrar Dene",
    "tba": "Açıklanacak",
    "rank": "Sıra #{number}",
    "saleType": "Satış Tipi",
    "totalAiScore": "Toplam YZ Puanı",
    "points": "Puan",
    "tokenomics": "Tokenomiks",
    "security": "Güvenlik",
    "social": "Sosyal",
    "market": "Piyasa",
    "insights": "İçgörüler"
  },

  // Upcoming token sale types explanations
  "saleType": {
    "IDO": "Initial DEX Offering - Merkeziyetsiz borsada (DEX) gerçekleştirilen token satışı",
    "IEO": "Initial Exchange Offering - Merkezi kripto para borsasında gerçekleştirilen token satışı",
    "ICO": "Initial Coin Offering - Erken yatırımcılara yeni projelerin token sunduğu ilk fonlama aşaması",
    "SHO": "Strong Holder Offering - Uzun vadeli token sahiplerine öncelik veren token satışı",
    "Seed": "Seed Round - Halka açılmadan önceki erken özel fonlama turu",
    "IGO": "Initial Game Offering - Blockchain oyun projelerine odaklanan fonlama",
    "ISO": "Initial Stake Offering - Stake mekanizması aracılığıyla token dağıtımı"
  },

  // Listing date options
  "listingDate": {
    "24hours": "Son 24 Saat",
    "7days": "Son 7 Gün",
    "14days": "Son 14 Gün",
    "30days": "Son 30 Gün",
    "90days": "Son 90 Gün"
  },

  // Upcoming IDO Tour related strings
  "upcomingTour": {
    "welcome": {
      "title": "Yaklaşan IDO'lara Hoş Geldiniz",
      "description": "Yaklaşan IDO'ların özelliklerine hızlı bir tur yapmak ister misiniz?",
      "info": "Yaklaşan token satışlarını nasıl filtreleyeceğinizi, proje detaylarını anlayacağınızı ve projeleri piyasaya sürülmeden önce değerlendireceğinizi öğrenin.",
      "dontShowAgain": "Bunu bir daha gösterme",
      "skipButton": "Şimdilik atla",
      "startButton": "Tura Başla"
    },
    "steps": {
      "overview": {
        "title": "Yaklaşan IDO'lara Genel Bakış",
        "description": "Yeni token lansmanlarını piyasaya çıkmadan önce keşfedip değerlendirebileceğiniz Yaklaşan IDO'lar sayfasına hoş geldiniz.",
        "details": "Farklı blockchain'ler ve launchpad'ler arasındaki yaklaşan token satışlarını inceleyin, filtrelerin ve analiz edin."
      },
      "filters": {
        "title": "Filtre Seçenekleri",
        "description": "Yatırım kriterlerinize uyan belirli token satış türlerini bulmak için bu filtreleri kullanın.",
        "details": "Satış türü, launchpad, kategori, blockchain veya yatırımcılara göre filtreleyin.",
        "action": "Listenin nasıl güncellendiğini görmek için bir filtre seçmeyi deneyin."
      },
      "search": {
        "title": "Ara & Bul",
        "description": "İsim veya sembol ile herhangi bir yaklaşan token satışını hızlıca arayın.",
        "details": "Sonuçlar yazdıkça gerçek zamanlı olarak güncellenir."
      },
      "projectInfo": {
        "title": "Proje Bilgileri",
        "description": "Her satır, yaklaşan bir token satışı hakkında ayrıntılı bilgiler içerir.",
        "details": "Ayrıntılı analiz için herhangi bir satıra tıklayın.",
        "action": "Daha fazla bilgi görmek için bir projenin üzerine gelmeyi deneyin."
      },
      "initialCap": {
        "title": "Başlangıç Piyasa Değeri",
        "description": "Tokenin listelenme anındaki beklenen başlangıç piyasa değeri.",
        "details": "Token fiyatı × TGE'deki dolaşımdaki arz olarak hesaplanır."
      },
      "score": {
        "title": "CoinScout Puanı",
        "description": "Özel puanlama sistemimiz, projenin genel kalitesini ve potansiyelini değerlendirir.",
        "details": "Tokenomiks, güvenlik, sosyal medya aktivitesi ve daha fazlasına dayanır."
      },
      "launchDate": {
        "title": "Başlangıç Tarihi",
        "description": "Tokenin işlem için kullanılabilir olacağı planlanmış tarih.",
        "details": "Yatırım stratejinizi hazırlamak için yaklaşan lansmanlardan haberdar olun."
      },
      "pagination": {
        "title": "Sayfa Gezinme",
        "description": "Yaklaşan token satışları listesinde gezinin.",
        "details": "Sayfa başına görüntülenen proje sayısını ayarlayın."
      },
      "completion": {
        "title": "Hazırsınız!",
        "description": "Yaklaşan IDO'lar sayfasının turunu tamamladınız.",
        "details": "Bir sonraki yatırım fırsatınızı bulmak için yaklaşan token satışlarını keşfetmeye ve analiz etmeye başlayın.",
        "help": "Rehberli Tur düğmesine tıklayarak bu turu istediğiniz zaman yeniden başlatabilirsiniz."
      }
    }
  },

  // Flat format for upcoming (for backward compatibility)
  "upcoming:title": "Yaklaşan Token Satışları",
  "upcoming:subtitle": "Piyasaya sürülmeden önce yeni tokenleri keşfedin ve değerlendirin",
  "upcoming:filters.title": "Filtreler",
  "upcoming:filters.saleType": "Satış Tipi",
  "upcoming:filters.allTypes": "Tüm Tipler",
  "upcoming:filters.launchpad": "Launchpad",
  "upcoming:filters.allLaunchpads": "Tüm Launchpad'ler",
  "upcoming:filters.category": "Kategori",
  "upcoming:filters.allCategories": "Tüm Kategoriler",
  "upcoming:filters.blockchain": "Blockchain",
  "upcoming:filters.allBlockchains": "Tüm Blockchain'ler",
  "upcoming:filters.investor": "Yatırımcı",
  "upcoming:filters.allInvestors": "Tüm Yatırımcılar",
  "upcoming:filters.projectScore": "Proje Puanı",
  "upcoming:filters.listingDate": "Listelenme Tarihi",
  "upcoming:filters.reset": "Filtreleri Sıfırla",
  "upcoming:filters.apply": "Filtreleri Uygula",
  "upcoming:table.name": "İsim",
  "upcoming:table.launchDate": "Başlangıç Tarihi",
  "upcoming:table.initialCap": "Başlangıç Piyasa Değeri",
  "upcoming:table.totalRaised": "Toplam Fon",
  "upcoming:table.score": "Puan",
  "upcoming:table.actions": "İşlemler",
  "upcoming:search": "Yaklaşan token satışlarını ara...",
  "upcoming:noResults": "Yaklaşan token satışı bulunamadı",
  "upcoming:loading": "Yaklaşan token satışları yükleniyor...",
  "upcoming:error": "Yaklaşan token satışları yüklenirken hata oluştu",
  "upcoming:retryButton": "Tekrar Dene",
  "upcoming:tba": "Açıklanacak",
  "upcoming:rank": "Sıra #{number}",
  "upcoming:saleType": "Satış Tipi",
  "upcoming:totalAiScore": "Toplam YZ Puanı",
  "upcoming:points": "Puan",
  "upcoming:tokenomics": "Tokenomiks",
  "upcoming:security": "Güvenlik",
  "upcoming:social": "Sosyal",
  "upcoming:market": "Piyasa",
  "upcoming:insights": "İçgörüler",

  // Methodology section translations
  "methodology": {
    "whatAreWeScoring": "Neyi Puanlıyoruz",
    "whyIsThisImportant": "Bu Neden Önemli",
    "scoringLevels": "Puanlama Seviyeleri"
  },

  // Profile section translations
  "profile": {
    "yourProfile": "Profiliniz",
    "howOthersSeeYou": "Diğer kullanıcılar sizi platformda böyle görecek.",
    "verified": "Doğrulanmış",
    "unverified": "Doğrulanmamış",
    "memberSince": "Üyelik tarihi",
    "unknown": "Bilinmiyor",
    "status": "Durum",
    "active": "Aktif",
    "plan": "Plan",
    "unknownPlan": "Bilinmeyen Plan",
    "planStatus": "Plan Durumu",
    "started": "Başlangıç",
    "expires": "Bitiş",
    "lastLogin": "Son giriş",
    "never": "Hiçbir zaman",
    "profileCompletion": "Profil tamamlanma",
    "improveTip": "Profilinizi geliştirmek için biyografi ve profil resmi ekleyin.",
    "signOut": "Çıkış Yap",
    "areYouSure": "Emin misiniz?",
    "signOutConfirmation": "Hesabınızdan çıkış yapacaksınız. İstediğiniz zaman tekrar giriş yapabilirsiniz.",
    "cancel": "İptal",
    "personalInformation": "Kişisel Bilgiler",
    "updateDescription": "Kişisel bilgilerinizi ve profilinizin nasıl göründüğünü güncelleyin.",
    "username": "Kullanıcı adı",
    "email": "E-posta",
    "verified": "Doğrulanmış",
    "unverified": "Doğrulanmamış",
    "emailPlaceholder": "<EMAIL>",
    "emailSent": "E-posta gönderildi",
    "verificationEmailSent": "Doğrulama e-postası başarıyla gönderildi. Lütfen gelen kutunuzu kontrol edin.",
    "error": "Hata",
    "emailSendError": "E-posta gönderilirken bir hata oluştu. Lütfen tekrar deneyin.",
    "resendVerification": "Doğrulama e-postasını tekrar gönder",
    "membershipTier": "Üyelik Seviyesi",
    "pro": "Pro",
    "premium": "Premium",
    "free": "Ücretsiz",
    "manageSubscription": "Aboneliğinizi şurada yönetin:",
    "membership": "Üyelik",
    "tab": "sekmesi",
    "downloadData": "Verilerinizi İndirin",
    "deleteAccount": "Hesabı Sil",
    "absolutelySure": "Kesinlikle emin misiniz?",
    "deleteWarning": "Bu işlem geri alınamaz. Bu, hesabınızı kalıcı olarak silecek ve verilerinizi sunucularımızdan kaldıracaktır.",
    "title": "Profil",
    "description": "Hesap ayarlarınızı ve tercihlerinizi yönetin.",
    "saveChanges": "Değişiklikleri Kaydet",
    "tabs": {
      "profile": "Profil",
      "membership": "Üyelik",
      "notifications": "Bildirimler"
    }
  }
};

export default tr;