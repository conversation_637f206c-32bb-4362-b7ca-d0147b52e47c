import React, {
  useState,
  useEffect,
  useRef,
  use<PERSON><PERSON>back,
  useMemo,
} from "react";
import { useLocation } from "wouter";
import { motion, AnimatePresence } from "framer-motion";
import { CoinLogo } from "@/components/CoinLogo";
import CoinService, { CoinFilters } from "../lib/services/CoinService";
import CoinTableRow from "@/components/CoinTableRow";
import { useIsMobile } from "@/hooks/use-mobile";
import DashboardService, {
  TopGainer,
  NewListing,
  GemCoin,
  UpcomingIDO,
  TopAirdrop,
} from "../lib/services/DashboardService";

const safeScoreValue = (score: string | number | undefined): number => {
  if (score === undefined) return 0;
  return typeof score === "string" ? parseFloat(score) : score;
};
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardHeader,
  Card<PERSON><PERSON>le,
  CardDescription,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import { useFavorites } from "@/hooks/useFavorites";
import { CoinTableHeader } from "@/components/CoinTableHeader";
import { useLanguage } from "@/contexts/LanguageContext";
import { isTourCompleted } from "@/lib/tourUtils";
import {
  TrendingUp,
  TrendingDown,
  Sparkles,
  Filter,
  RefreshCw,
  Search,
  X,
  Clock,
  Rocket,
} from "lucide-react";

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { Pagination } from "@/components/Pagination";
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  FilterControls,
  FilterControlsState,
} from "@/components/FilterControls";
import { Input } from "@/components/ui/input";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { CreateAlertButton } from "@/components/alerts/CreateAlertButton";

import { Coin as BaseCoin, CoinScore as BaseCoinScore } from "@/types/coin";

// Extend the imported Coin interface to include additional properties needed by CoinList
interface Coin extends BaseCoin {
  price?: number;
  priceChanges?: {
    "24h": number;
    "7d": number;
    "30d": number;
    "90d": number;
    "1y": number;
  };
  demo?: boolean; // Demo coin işaretleyici
}

const TotalScoreIndicator = ({
  score,
  status,
}: {
  score: number;
  status: string;
}) => {
  const getRating = (score: number) => {
    if (score >= 85)
      return {
        text: "Excellent",
        color: "text-emerald-400",
        bgColor: "bg-emerald-400/20",
      };
    if (score >= 75)
      return {
        text: "Good",
        color: "text-blue-400",
        bgColor: "bg-blue-400/20",
      };
    if (score >= 65)
      return {
        text: "Fair",
        color: "text-yellow-400",
        bgColor: "bg-yellow-400/20",
      };
    if (score >= 50)
      return {
        text: "Poor",
        color: "text-orange-400",
        bgColor: "bg-orange-400/20",
      };
    return { text: "Bad", color: "text-red-400", bgColor: "bg-red-400/20" };
  };

  const rating = getRating(score);

  return (
    <motion.div
      className="flex items-center gap-2"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className="relative w-14 h-14"
        initial={{ rotate: -90 }}
        animate={{ rotate: 0 }}
        transition={{ duration: 1, ease: "easeOut" }}
      >
        <svg className="w-full h-full transform -rotate-90">
          <defs>
            <linearGradient
              id={`scoreGradient-${score}`}
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <stop
                offset="0%"
                stopColor={
                  score >= 85
                    ? "#00FF85"
                    : score >= 75
                      ? "#3B82F6"
                      : score >= 65
                        ? "#FFB800"
                        : score >= 50
                          ? "#FF7A00"
                          : "#FF3B3B"
                }
              />
              <stop
                offset="100%"
                stopColor={
                  score >= 85
                    ? "#00FF85"
                    : score >= 75
                      ? "#3B82F6"
                      : score >= 65
                        ? "#FFB800"
                        : score >= 50
                          ? "#FF7A00"
                          : "#FF3B3B"
                }
              />
            </linearGradient>
          </defs>
          <circle
            cx="27"
            cy="27"
            r="22"
            fill="none"
            stroke="currentColor"
            strokeWidth="3.5"
            className="text-muted/10"
          />
          <motion.circle
            cx="27"
            cy="27"
            r="22"
            fill="none"
            stroke={`url(#scoreGradient-${score})`}
            strokeWidth="3.5"
            strokeLinecap="round"
            strokeDasharray={`${2 * Math.PI * 22}`}
            initial={{ strokeDashoffset: 2 * Math.PI * 22 }}
            animate={{ strokeDashoffset: 2 * Math.PI * 22 * (1 - score / 100) }}
            transition={{ duration: 1.5, ease: "easeOut" }}
          />
        </svg>
        <motion.div
          className="absolute inset-0 flex items-center justify-center"
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 0.85, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <span
            className={`text-sm font-semibold opacity-90 ${score >= 85 ? "text-emerald-400" : score >= 75 ? "text-blue-400" : score >= 65 ? "text-yellow-400" : "text-red-400"}`}
          >
            {score}
          </span>
        </motion.div>
      </motion.div>
      <motion.span
        className={cn("text-xs font-semibold", rating.color)}
        initial={{ opacity: 0, x: -10 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5, delay: 0.7 }}
      >
        {rating.text}
      </motion.span>
    </motion.div>
  );
};

// Ortak CoinHoverContent bileşenini kullanıyoruz
import CoinHoverContent from "@/components/CoinHoverContent";
function CoinListPage() {
  const [location, setLocation] = useLocation();
  const [hasHorizontalScroll, setHasHorizontalScroll] = useState(false);
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const { toggleFavorite, isFavorite } = useFavorites();
  const { t } = useLanguage(); // Add language context

  const [tableCoins, setTableCoins] = useState<Coin[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const [dashboardHighlights, setDashboardHighlights] = useState<{
    topGainers: TopGainer[];
    newListings: NewListing[];
    gemCoins: GemCoin[];
    upcomingIDOs: UpcomingIDO[];
    topAirdrops: TopAirdrop[];
  }>({
    topGainers: [],
    newListings: [],
    gemCoins: [],
    upcomingIDOs: [],
    topAirdrops: [],
  });
  const [isLoadingHighlights, setIsLoadingHighlights] = useState(true);
  const [filterDialogOpen, setFilterDialogOpen] = useState(false);
  // Initialize filter state from localStorage or use defaults
  const [currentFilters, setCurrentFilters] = useState<FilterControlsState>(() => {
    // Try to get saved filters from localStorage
    const savedFilters = localStorage.getItem('coinlistFilters');
    if (savedFilters) {
      try {
        return JSON.parse(savedFilters);
      } catch (error) {
        console.error("Error parsing saved filters:", error);
      }
    }

    // Default values if no saved filters are found
    return {
      marketCapRange: [1000000, 100000000000], // Default values (1M and 100B)
      projectScoreRange: [0, 100],
      categories: [],
      chains: [],
    };
  });

  const getInitialSearchFromUrl = () => {
    if (typeof window !== "undefined") {
      const searchParams = new URLSearchParams(window.location.search);
      const urlSearch = searchParams.get("search");
      return urlSearch || "";
    }
    return "";
  };

  // Function to find which page a coin is on
  const findCoinPage = useCallback((coinId: string, coins: Coin[], pageSize: number) => {
    const coinIndex = coins.findIndex(coin => coin.id === coinId);
    if (coinIndex === -1) return 1; // Default to first page if not found

    return Math.floor(coinIndex / pageSize) + 1;
  }, []);

  const [search, setSearch] = useState(getInitialSearchFromUrl);
  const [debouncedSearch, setDebouncedSearch] = useState(
    getInitialSearchFromUrl,
  );

  // Get coinId from URL if present
  const getInitialCoinIdFromUrl = () => {
    if (typeof window !== "undefined") {
      const searchParams = new URLSearchParams(window.location.search);
      return searchParams.get("coinId") || null;
    }
    return null;
  };

  const initialCoinId = getInitialCoinIdFromUrl();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: "asc" | "desc" | false;
  }>({
    key: "",
    direction: false,
  });
  const [showHighlights, setShowHighlights] = useState(() => {
    const saved = localStorage.getItem("showHighlights");
    return saved !== null ? JSON.parse(saved) : true;
  });

  const formatTimeWithLabels = useCallback((seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${remainingSeconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      return `${remainingSeconds}s`;
    }
  }, []);

  const [nextUpdate, setNextUpdate] = useState<number>(() => {
    const now = new Date();
    const currentHour = now.getHours();
    const nextUpdateHour = currentHour + 2 - (currentHour % 2);
    const nextUpdateTime = new Date();
    nextUpdateTime.setHours(nextUpdateHour, 0, 0, 0);
    if (nextUpdateTime.getTime() < now.getTime()) {
      nextUpdateTime.setHours(nextUpdateHour + 2, 0, 0, 0);
    }
    return Math.floor((nextUpdateTime.getTime() - now.getTime()) / 1000);
  });

  useEffect(() => {
    let lastTick = performance.now();
    let accumulatedTime = 0;
    let animationFrameId: number;

    const updateTimer = (timestamp: number) => {
      const delta = timestamp - lastTick;
      lastTick = timestamp;

      accumulatedTime += delta;

      if (accumulatedTime >= 1000) {
        setNextUpdate((prev) => {
          if (prev <= 1) {
            return 7200;
          }
          return prev - 1;
        });

        accumulatedTime = accumulatedTime % 1000;
      }

      animationFrameId = requestAnimationFrame(updateTimer);
    };

    animationFrameId = requestAnimationFrame(updateTimer);

    return () => {
      cancelAnimationFrame(animationFrameId);
    };
  }, []);

  useEffect(() => {
    localStorage.setItem("showHighlights", JSON.stringify(showHighlights));
  }, [showHighlights]);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearch(search);

      if (search.trim()) {
        const url = new URL(window.location.href);
        url.searchParams.set("search", search.trim());
        window.history.replaceState({}, "", url.toString());
      } else if (window.location.search.includes("search=")) {
        const url = new URL(window.location.href);
        url.searchParams.delete("search");
        window.history.replaceState({}, "", url.toString());
      }
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [search]);

  const requestSort = useCallback((key: string) => {
    setSortConfig((prevState) => {
      if (prevState.key === key) {
        if (prevState.direction === "asc") {
          return { key, direction: "desc" };
        } else if (prevState.direction === "desc") {
          return { key, direction: false };
        } else {
          return { key, direction: "asc" };
        }
      }

      return { key, direction: "asc" };
    });
  }, []);

  const openFilterDialog = () => {
    setFilterDialogOpen(true);
  };

  const fetchData = async (filters = {}) => {
    try {
      setIsLoading(true);
      // Add change=null parameter for regular CoinList page
      const coinListFilters = {
        ...filters,
        change: null,
      };
      const response = await CoinService.getCoinsList(coinListFilters);
      setTableCoins(response);
      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching coin list:", error);
      setIsLoading(false);
    }
  };

  const handleApplyFilters = (
    filters: FilterControlsState & { isReset?: boolean },
  ) => {
    // Update current filters state
    setCurrentFilters(filters);

    // Save filters to localStorage for persistence
    localStorage.setItem('coinlistFilters', JSON.stringify(filters));

    if (filters.isReset) {
      // When resetting, also clear the saved filters in localStorage
      localStorage.removeItem('coinlistFilters');

      const resetApiFilters: CoinFilters = {
        marketcap_min: null,
        marketcap_max: null,
        score_min: null,
        score_max: null,
        selectedcategories: null,
        selectedchains: null,
        change: null,
      };

      console.log("Resetting filters - using null values:", resetApiFilters);
      fetchData(resetApiFilters);
      return;
    }

    const apiFilters: CoinFilters = {
      // FilterControls'da değerler zaten milyon cinsinden gösteriliyor ve dönüştürülüyor
      // Bu nedenle burada fazladan çarpım yapmıyoruz
      marketcap_min: filters.marketCapRange[0],
      marketcap_max: filters.marketCapRange[1],
      score_min: filters.projectScoreRange[0],
      score_max: filters.projectScoreRange[1],
      selectedcategories:
        filters.categories.length > 0 ? filters.categories : null, // Kategori ID'leri artık doğrudan kullanılıyor
      selectedchains: filters.chains.length > 0 ? filters.chains : null,
      change: null,
    };

    console.log("Applying filters:", apiFilters);
    fetchData(apiFilters);
  };

  // Load saved filters and apply them when the component mounts
  useEffect(() => {
    const loadAndApplySavedFilters = async () => {
      try {
        // Try to get saved filters from localStorage
        const savedFilters = localStorage.getItem('coinlistFilters');

        if (savedFilters) {
          const parsedFilters = JSON.parse(savedFilters);
          console.log("Loaded saved filters:", parsedFilters);

          // Update filter state
          setCurrentFilters(parsedFilters);

          // Convert to API filter format and fetch data
          const apiFilters: CoinFilters = {
            marketcap_min: parsedFilters.marketCapRange[0],
            marketcap_max: parsedFilters.marketCapRange[1],
            score_min: parsedFilters.projectScoreRange[0],
            score_max: parsedFilters.projectScoreRange[1],
            selectedcategories: parsedFilters.categories.length > 0 ? parsedFilters.categories : null,
            selectedchains: parsedFilters.chains.length > 0 ? parsedFilters.chains : null,
            change: null,
          };

          // Use the saved filters to fetch data
          await fetchData(apiFilters);
        } else {
          // If no saved filters, just fetch data with default values
          await fetchData();
        }
      } catch (error) {
        console.error("Error loading or applying saved filters:", error);
        // Fallback to default fetch if there's an error with saved filters
        await fetchData();
      }
    };

    const fetchDashboardHighlights = async () => {
      try {
        setIsLoadingHighlights(true);
        const highlights = await DashboardService.getDashboardHighlights();
        setDashboardHighlights(highlights);
        setIsLoadingHighlights(false);
      } catch (error) {
        console.error("Error fetching dashboard highlights:", error);
        setIsLoadingHighlights(false);
      }
    };

    // Load and apply filters first, then fetch highlights
    loadAndApplySavedFilters();
    fetchDashboardHighlights();
  }, []);

  const filteredCoins = useMemo(() => {
    let result = tableCoins;

    if (debouncedSearch.trim()) {
      const searchLower = debouncedSearch.toLowerCase().trim();
      result = result.filter(
        (coin) =>
          coin.name.toLowerCase().includes(searchLower) ||
          coin.symbol.toLowerCase().includes(searchLower),
      );
    }

    if (sortConfig.key && sortConfig.direction !== false) {
      // Önce demo ve gerçek coinleri ayır
      const realCoins = result.filter(coin => !coin.demo);
      const demoCoins = result.filter(coin => coin.demo);

      // Gerçek coinleri sırala
      const sortedRealCoins = [...realCoins].sort((a, b) => {
        let aValue, bValue;

        switch (sortConfig.key) {
          case "rank":
            aValue = a.rank;
            bValue = b.rank;
            break;
          case "name":
            aValue = a.name.toLowerCase();
            bValue = b.name.toLowerCase();
            break;
          case "tokenomics":
            aValue = a.tokenomics.score;
            bValue = b.tokenomics.score;
            break;
          case "security":
            aValue = a.security.score;
            bValue = b.security.score;
            break;
          case "social":
            aValue = a.social.score;
            bValue = b.social.score;
            break;
          case "market":
            aValue = a.market.score;
            bValue = b.market.score;
            break;
          case "insights":
            aValue = a.insights.score;
            bValue = b.insights.score;
            break;
          case "totalScore":
            aValue = a.totalScore.score;
            bValue = b.totalScore.score;
            break;
          case "sevenDayChange":
            aValue = a.sevenDayChange;
            bValue = b.sevenDayChange;
            break;
          default:
            aValue = a.rank;
            bValue = b.rank;
        }

        if (sortConfig.direction === "asc") {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });

      // Demo coinleri de sırala (aynı mantıkla)
      const sortedDemoCoins = [...demoCoins].sort((a, b) => {
        let aValue, bValue;

        switch (sortConfig.key) {
          case "rank":
            aValue = a.rank;
            bValue = b.rank;
            break;
          case "name":
            aValue = a.name.toLowerCase();
            bValue = b.name.toLowerCase();
            break;
          case "tokenomics":
            aValue = a.tokenomics.score;
            bValue = b.tokenomics.score;
            break;
          case "security":
            aValue = a.security.score;
            bValue = b.security.score;
            break;
          case "social":
            aValue = a.social.score;
            bValue = b.social.score;
            break;
          case "market":
            aValue = a.market.score;
            bValue = b.market.score;
            break;
          case "insights":
            aValue = a.insights.score;
            bValue = b.insights.score;
            break;
          case "totalScore":
            aValue = a.totalScore.score;
            bValue = b.totalScore.score;
            break;
          case "sevenDayChange":
            aValue = a.sevenDayChange;
            bValue = b.sevenDayChange;
            break;
          default:
            aValue = a.rank;
            bValue = b.rank;
        }

        if (sortConfig.direction === "asc") {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });

      // Gerçek coinler önce, demo coinler sonra
      result = [...sortedRealCoins, ...sortedDemoCoins];
    }

    return result;
  }, [debouncedSearch, sortConfig, tableCoins]);

  const totalPages = useMemo(
    () => Math.ceil(filteredCoins.length / pageSize),
    [filteredCoins.length, pageSize],
  );

  const paginatedCoins = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return filteredCoins.slice(startIndex, startIndex + pageSize);
  }, [filteredCoins, currentPage, pageSize]);

  useEffect(() => {
    const checkForHorizontalScroll = () => {
      if (tableContainerRef.current) {
        const { scrollWidth, clientWidth } = tableContainerRef.current;
        setHasHorizontalScroll(scrollWidth > clientWidth);
      }
    };

    const timer = setTimeout(() => {
      checkForHorizontalScroll();
    }, 100);

    window.addEventListener("resize", checkForHorizontalScroll);

    return () => {
      window.removeEventListener("resize", checkForHorizontalScroll);
      clearTimeout(timer);
    };
  }, [paginatedCoins]);

  // Effect to set the correct page when a coinId is present in the URL
  useEffect(() => {
    if (initialCoinId && filteredCoins.length > 0) {
      const targetPage = findCoinPage(initialCoinId, filteredCoins, pageSize);
      setCurrentPage(targetPage);

      // Clear the coinId from URL after navigating to the correct page
      if (typeof window !== "undefined") {
        const url = new URL(window.location.href);
        url.searchParams.delete("coinId");
        window.history.replaceState({}, '', url.toString());
      }
    }
  }, [initialCoinId, filteredCoins, pageSize, findCoinPage]);

  // Effect to scroll to top when current page changes
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [currentPage]);

  return (

    <div className="w-full py-6 pr-1 md:pr-3 max-w-[1920px] mx-auto coin-list-main">
      <div id="top"></div>
      <div className="flex flex-col gap-2 pl-0 ml-0">
        <div
          id="coin-list-page-header"
          className="flex flex-col lg:flex-row items-center justify-between"
        >
          <div className="">
            <div className="flex items-center  gap-3">
              <h1 className="text-2xl font-bold tracking-tight text-[#66B2FF]">
                {t(
                  "coinlist:aiPoweredTitle",
                  "coinlist",
                  "AI-Powered & Data-Driven Crypto Fundamental Ratings",
                )}
              </h1>
              {}
            </div>

          </div>
          <div className="flex items-center gap-2 actions-section ">
            <div className="flex flex-row max-sm:flex-col items-center gap-4 border-transparent rounded-md p-3">
              <div className="flex items-center gap-2 hover:bg-[#132F4C]/90 transition-colors rounded-md p-2 pl-3 pr-2.5 border border-[#1E4976]/50">
                <Label
                  htmlFor="highlights-toggle"
                  className="text-sm font-semibold text-[#66B2FF]"
                >
                  {t("coinlist:highlights", "coinlist", "Highlights")}
                </Label>
                <Switch
                  id="highlights-toggle"
                  checked={showHighlights}
                  onCheckedChange={setShowHighlights}
                  className="data-[state=checked]:bg-[#66B2FF] data-[state=unchecked]:bg-[#0A4D5F] h-5 w-10 ml-1 border border-[#1E4976]/50 [&>span]:border [&>span]:border-[#4f89e2]/30 [&>span]:data-[state=unchecked]:bg-[#1a3a65] [&>span]:data-[state=checked]:bg-[#1a3a65]"
                />
              </div>

              <div className="flex items-center gap-2.5 bg-[#132F4C]/0 hover:bg-[#132F4C]/0 transition-colors rounded-md p-2 px-3 border border-[#1E4976]/50">
                <RefreshCw className="h-4 w-4 text-[#66B2FF] group-hover:rotate-1880 transition-transform duration-700" />
                <div className="flex items-center">
                  <span className="text-sm font-semibold text-[#66B2FF] mr-2">
                    Next Data Update:
                  </span>
                  <div className="flex items-center">
                    <Clock className="h-3.5 w-3.5 mr-1.5 text-[#66B2FF]" />
                    <span
                      className="text-[#66B2FF] text-opacity-90 text-sm"
                      title="Data updates every 2 hours"
                    >
                      {formatTimeWithLabels(nextUpdate)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <AnimatePresence>
        {showHighlights && (
          <motion.div
            id="highlights-section"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="grid grid-cols-1 sm:grid-cols-3 gap-6 2xl:gap-8 mt-6"
          >
            <Card className="bg-card/50 backdrop-blur-sm border-border/50 group/card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 px-4 pt-4 2xl:px-6 2xl:pt-6 ">
                <CardTitle className="text-xs pt-2 md:text-base font-semibold flex items-center gap-1.5 text-[#E7EBF0]/80">
                  <TrendingUp className="h-6 w-6 text-emerald-500 group-hover/card:animate-spin-once" />
                  {t("highlights:topGainers", "highlights", "Top Movers")}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-2 sm:p-4 2xl:p-6 pt-0 space-y-1">
                <div className="flex px-1">
                  <span className="text-[0.65rem] w-full text-right  text-[#E7EBF0]/50">
                    {t("highlights:score", "highlights", "Score")}
                  </span>
                </div>
                <div className="space-y-1.5 sm:space-y-2 mb-4 sm:mb-6">
                  {isLoadingHighlights
                    ? Array(3)
                        .fill(0)
                        .map((_, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between whitespace-nowrap animate-pulse"
                          >
                            <div className="flex items-center gap-1.5 overflow-hidden">
                              <div className="w-6 h-6 rounded-full bg-gray-700/30 flex-shrink-0" />
                              <div className="h-4 w-16 bg-gray-700/30 rounded" />
                            </div>
                            <div className="flex items-center gap-2 flex-shrink-0">
                              <div className="h-4 w-10 bg-gray-700/30 rounded" />
                              <div className="w-8 h-8 rounded-full bg-gray-700/30 flex-shrink-0" />
                            </div>
                          </div>
                        ))
                    : dashboardHighlights.topGainers.slice(0, 3).map((coin) => (
                        <div
                          key={coin.id}
                          className="flex items-center justify-between whitespace-nowrap cursor-pointer hover:bg-[#132F4C]/30 transition-colors rounded-md px-1"
                          onClick={() => setLocation(`/coin/${coin.id}`)}
                        >
                          <div className="flex items-center gap-1.5 overflow-hidden">
                            {coin.image ? (
                              <img
                                src={coin.image}
                                alt={coin.symbol}
                                className="w-6 h-6 rounded-full opacity-90 flex-shrink-0"
                              />
                            ) : (
                              <CoinLogo
                                symbol={coin.symbol}
                                size="highlight"
                                className="opacity-90 flex-shrink-0"
                              />
                            )}
                            <span className="font-semibold text-xs text-[#E7EBF0]/70 truncate max-w-[100px]">
                              {coin.name}
                            </span>
                          </div>
                          <div className="flex items-center gap-2 flex-shrink-0">
                            <span
                              className={cn(
                                "text-sm whitespace-nowrap",
                                parseFloat(String(coin.gainPercentage)) >= 0
                                  ? "text-emerald-500"
                                  : "text-red-500",
                              )}
                            >
                              {parseFloat(String(coin.gainPercentage)) >= 0
                                ? "+"
                                : ""}
                              {typeof coin.gainPercentage === "string"
                                ? parseFloat(coin.gainPercentage).toFixed(1)
                                : coin.gainPercentage.toFixed(1)}
                              %
                            </span>
                            <div
                              className={cn(
                                "w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold",
                                safeScoreValue(coin.totalScore) >= 90
                                  ? "bg-[#00D88A]/10 text-[#00D88A]"
                                  : safeScoreValue(coin.totalScore) >= 75
                                    ? "bg-[#00B8D9]/10 text-[#00B8D9]"
                                    : safeScoreValue(coin.totalScore) >= 65
                                      ? "bg-[#FFAB00]/10 text-[#FFAB00]"
                                      : safeScoreValue(coin.totalScore) >= 50
                                        ? "bg-[#FF5630]/10 text-[#FF5630]"
                                        : "bg-[#FF3B3B]/10 text-[#FF3B3B]",
                              )}
                            >
                              {coin.totalScore}
                            </div>
                          </div>
                        </div>
                      ))}
                  <div className="pt-4 mt-4 border-t border-border/10">
                    <Button
                      variant="link"
                      className="text-primary hover:text-primary/80 text-xs h-9 border rounded-lg w-full transition-all duration-200 dark:border-[#1E4976]/20 dark:bg-[#0A1929]/50 dark:hover:bg-[#132F4C]/50 dark:hover:border-[#1E4976]/40"
                      onClick={() => setLocation("/top-movers")}
                    >
                      {t("coinlist:viewAll", "coinlist", "View All")}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card/50 backdrop-blur-sm border-border/50 group/card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 px-4 pt-4 2xl:px-6 2xl:pt-6">
                <CardTitle className="text-xs md:text-base font-semibold flex pt-2 items-center gap-1.5 text-[#E7EBF0]/80">
                  <Sparkles className="h-6 w-6 text-blue-500 group-hover/card:animate-spin-once" />
                  {t("highlights:newListings", "highlights", "New Listings")}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-2 sm:p-4 2xl:p-6 pt-0 space-y-2">
                <div className="flex px-1">
                  <span className="text-[0.65rem] w-full text-right  text-[#E7EBF0]/50">
                    {t("highlights:score", "highlights", "Score")}
                  </span>
                </div>

                <div className="space-y-1.5 sm:space-y-2">
                  {isLoadingHighlights
                    ? Array(3)
                        .fill(0)
                        .map((_, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between whitespace-nowrap animate-pulse"
                          >
                            <div className="flex items-center gap-1.5 overflow-hidden">
                              <div className="w-6 h-6 rounded-full bg-gray-700/30 flex-shrink-0" />
                              <div className="h-4 w-16 bg-gray-700/30 rounded" />
                            </div>
                            <div className="flex items-center gap-2 flex-shrink-0">
                              <div className="h-4 w-10 bg-gray-700/30 rounded" />
                              <div className="w-8 h-8 rounded-full bg-gray-700/30 flex-shrink-0" />
                            </div>
                          </div>
                        ))
                    : dashboardHighlights.newListings.slice(0, 3)
                        .map((coin) => (
                          <div
                            key={coin.id}
                            className="flex items-center justify-between whitespace-nowrap cursor-pointer hover:bg-[#132F4C]/30 transition-colors rounded-md px-1"
                            onClick={() => setLocation(`/coin/${coin.id}`)}
                          >
                            <div className="flex items-center gap-1.5 overflow-hidden">
                              {coin.image ? (
                                <img
                                  src={coin.image}
                                  alt={coin.symbol}
                                  className="w-6 h-6 rounded-full opacity-90 flex-shrink-0"
                                />
                              ) : (
                                <CoinLogo
                                  symbol={coin.symbol}
                                  size="highlight"
                                  className="opacity-90 flex-shrink-0"
                                />
                              )}
                              <span className="font-semibold text-xs text-[#E7EBF0]/70 truncate max-w-[100px]">
                                {coin.name}
                              </span>
                            </div>
                            <div className="flex items-center gap-2 whitespace-nowrap flex-shrink-0">
                              <span className="text-xs px-2 py-0.5 text-[#E7EBF0]/70">
                                {coin.daysListed === 0
                                  ? "Today"
                                  : coin.daysListed === 1
                                    ? "Yesterday"
                                    : `${coin.daysListed}d ago`}
                              </span>
                              <div
                                className={cn(
                                  "w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold",
                                  safeScoreValue(coin.totalScore) >= 90
                                    ? "bg-[#00D88A]/10 text-[#00D88A]"
                                    : safeScoreValue(coin.totalScore) >= 75
                                      ? "bg-[#00B8D9]/10 text-[#00B8D9]"
                                      : safeScoreValue(coin.totalScore) >= 65
                                        ? "bg-[#FFAB00]/10 text-[#FFAB00]"
                                        : safeScoreValue(coin.totalScore) >= 50
                                          ? "bg-[#FF5630]/10 text-[#FF5630]"
                                          : "bg-[#FF3B3B]/10 text-[#FF3B3B]",
                                )}
                              >
                                {coin.totalScore}
                              </div>
                            </div>
                          </div>
                        ))}
                  <div className="pt-4 mt-4 border-t border-border/10">
                    <Button
                      variant="link"
                      className="text-[#66B2FF] hover:text-[#99CCFF] text-xs h-9 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 dark:border-[#1E4976]/20 dark:bg-[#0A1929]/50 dark:hover:bg-[#132F4C]/50 dark:hover:border-[#1E4976]/40 w-full"
                      onClick={() => setLocation("/newly-listed-coins")}
                    >
                      {t("coinlist:viewAll", "coinlist", "View All")}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {}

            <Card className="bg-card/40 backdrop-blur-sm border-border/40 group/card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 px-4 pt-4 2xl:px-6 2xl:pt-6">
                <CardTitle className="text-xs md:text-base font-semibold pt-2 flex items-center gap-1.5 text-[#E7EBF0]/80">
                  <Rocket className="h-6 w-6 text-purple-500 group-hover/card:animate-spin-once" />
                  {t("highlights:upcomingIDOs", "highlights", "Upcoming IDOs")}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-2 sm:p-4 2xl:p-6 pt-0 space-y-2">
                <div className="flex px-1">
                  <span className="text-[0.65rem] w-full text-right  text-[#E7EBF0]/50">
                    {t("highlights:score", "highlights", "Score")}
                  </span>
                </div>
                <div className="space-y-1.5 sm:space-y-2">
                  {isLoadingHighlights
                    ? Array(3)
                        .fill(0)
                        .map((_, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between whitespace-nowrap animate-pulse"
                          >
                            <div className="flex items-center gap-1.5 overflow-hidden">
                              <div className="w-6 h-6 rounded-full bg-gray-700/30 flex-shrink-0" />
                              <div className="h-4 w-16 bg-gray-700/30 rounded" />
                            </div>
                            <div className="flex items-center gap-2 flex-shrink-0">
                              <div className="h-4 w-10 bg-gray-700/30 rounded" />
                              <div className="w-8 h-8 rounded-full bg-gray-700/30 flex-shrink-0" />
                            </div>
                          </div>
                        ))
                    : dashboardHighlights.upcomingIDOs
                        .slice(0, 3)
                        .map((coin) => (
                          <div
                            key={coin.id}
                            className="flex items-center justify-between whitespace-nowrap cursor-pointer hover:bg-[#132F4C]/30 transition-colors rounded-md px-1"
                            onClick={() => setLocation(`/ido/${coin.id}`)}
                          >
                            <div className="flex items-center gap-1.5 overflow-hidden">
                              {coin.image ? (
                                <img
                                  src={coin.image}
                                  alt={coin.symbol}
                                  className="w-6 h-6 rounded-full opacity-90 flex-shrink-0"
                                />
                              ) : (
                                <CoinLogo
                                  symbol={coin.symbol}
                                  size="highlight"
                                  className="opacity-90 flex-shrink-0"
                                />
                              )}
                              <span className="font-semibold text-xs text-[#E7EBF0]/70 truncate max-w-[100px]">
                                {coin.name}
                              </span>
                            </div>
                            <div className="flex items-center gap-2 whitespace-nowrap flex-shrink-0">
                              <span className="text-xs px-2 py-0.5 text-[#E7EBF0]/70">
                                {coin.launchPlatform}
                              </span>
                              <div
                                className={cn(
                                  "w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold",
                                  safeScoreValue(coin.totalScore) >= 90
                                    ? "bg-[#00D88A]/10 text-[#00D88A]"
                                    : safeScoreValue(coin.totalScore) >= 75
                                      ? "bg-[#00B8D9]/10 text-[#00B8D9]"
                                      : safeScoreValue(coin.totalScore) >= 65
                                        ? "bg-[#FFAB00]/10 text-[#FFAB00]"
                                        : safeScoreValue(coin.totalScore) >= 50
                                          ? "bg-[#FF5630]/10 text-[#FF5630]"
                                          : "bg-[#FF3B3B]/10 text-[#FF3B3B]",
                                )}
                              >
                                {coin.totalScore}
                              </div>
                            </div>
                          </div>
                        ))}
                  <div className="pt-4 mt-4 border-t border-border/10">
                    <Button
                      variant="link"
                      className="text-[#66B2FF] hover:text-[#99CCFF] text-xs h-9 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 dark:border-[#1E4976]/20 dark:bg-[#0A1929]/50 dark:hover:bg-[#132F4C]/50 dark:hover:border-[#1E4976]/40 w-full"
                      onClick={() => setLocation("/upcoming")}
                    >
                      {t("coinlist:viewAll", "coinlist", "View All")}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {}
          </motion.div>
        )}
      </AnimatePresence>

      <div
        className="w-full overflow-visible h-px bg-white/5 my-8"
        aria-hidden="true"
      />

      <Card className="bg-transparent border-border/50 max-w-full mx-auto w-full">
        <CardHeader className="border-b border-border/40 pb-4 bg-transparent">
          <div className="flex lg:items-center flex-col lg:flex-row lg:justify-between">
            <div>
              <CardTitle>
                {t("coinlist:allCoins", "coinlist", "All Coins")}
              </CardTitle>
              <CardDescription className="py-2">
                {t(
                  "coinlist:coinDetailDescription",
                  "coinlist",
                  "Click on any coin for detailed analysis",
                )}
              </CardDescription>
            </div>
            <div
              id="search-filter-section"
              className="flex flex-col sm:flex-row sm:items-center gap-2 search-container"
            >
              <div className="relative">
                <div className="relative flex items-center w-full sm:w-[280px]">
                  <Search className="absolute left-3 h-4 w-4 text-[#66B2FF]/70" />
                  <Input
                    placeholder={t(
                      "coinlist:searchCoins",
                      "coinlist",
                      "Search coins...",
                    )}
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className={cn(
                      "search-input coin-search-input h-10 w-full bg-[#132F4C]/50 border-[#1E4976]/30 text-[#E7EBF0]/80 placeholder:text-[#E7EBF0]/50 pl-10",
                      "focus:bg-[#132F4C]/70 focus:border-[#1E4976]/50 transition-all duration-200 hover:bg-[#132F4C]/60 hover:border-[#1E4976]/40 rounded-md",
                      "focus:ring-1 focus:ring-[#66B2FF]/30 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-[#66B2FF]/30",
                      "transition-all duration-300 pr-8",
                      search !== debouncedSearch &&
                        "shadow-[0_0_0_1px_rgba(0,184,217,0.5)]",
                    )}
                    aria-label={t(
                      "coinlist:searchBox.ariaLabel",
                      "coinlist",
                      "Search coins",
                    )}
                  />
                  {}
                  {search !== debouncedSearch && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="absolute right-3 top-0 h-full flex items-center justify-center cursor-help">
                          <RefreshCw className="h-3.5 w-3.5 text-[#00B8D9]/70 animate-spin" />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent
                        side="right"
                        className="bg-[#132F4C]/95 border border-[#1E4976]/60 text-[#E7EBF0] p-2 text-xs rounded-md backdrop-blur-md"
                      >
                        <p>{t("coinlist:search.inProgress", "coinlist", "Search in progress...")}</p>
                        <p className="text-[10px] text-[#E7EBF0]/70 mt-1">
                          {t("coinlist:search.resultsUpdate", "coinlist", "Results will update automatically")}
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  )}
                  {search === debouncedSearch && search && (
                    <div
                      className="absolute right-3 top-0 h-full flex items-center justify-center cursor-pointer transition-transform duration-200"
                      onClick={() => setSearch("")}
                      title={t("coinlist:search.clearSearch", "coinlist", "Clear search")}
                      aria-label={t("coinlist:search.clearSearch", "coinlist", "Clear search")}
                    >
                      <X className="h-3.5 w-3.5 text-[#E7EBF0]/50 hover:text-[#E7EBF0]/80 transition-colors" />
                    </div>
                  )}
                </div>
              </div>
              <div
                className="grid grid-cols-2 gap-2 sm:flex sm:items-center sm:gap-2"
                data-tour="filter-alerts-section"
              >
                <Dialog
                  open={filterDialogOpen}
                  onOpenChange={setFilterDialogOpen}
                >
                  <DialogTrigger asChild>
                    <div className="filter-container">
                      <Button
                        variant="outline"
                        size="default"
                        className="w-full sm:w-auto filter-button coinlist-filter flex items-center gap-2 h-10 px-4
                          bg-[#132F4C]/50 border-[#1E4976]/30 text-[#E7EBF0]/80
                          hover:bg-[#132F4C]/70 hover:text-[#66B2FF] hover:border-[#1E4976]/50
                          focus:bg-[#132F4C]/70 focus:border-[#1E4976]/50
                          transition-all duration-200 rounded-md"
                      >
                        <Filter className="h-4 w-4" />
                        {t("coinlist:filters.button", "coinlist", "Filters")}
                      </Button>
                    </div>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[600px] bg-[#0A1929]/95 border border-[#1E4976]/60 backdrop-blur-md">
                    <DialogHeader>
                      <DialogTitle className="text-lg font-semibold text-[#66B2FF]">
                        {t(
                          "coinlist:filters.title",
                          "coinlist",
                          "Filter Options",
                        )}
                      </DialogTitle>
                      <DialogDescription className="text-[#E7EBF0]/70">
                        {t(
                          "coinlist:filters.description",
                          "coinlist",
                          "Customize your view with advanced filtering options",
                        )}
                      </DialogDescription>
                    </DialogHeader>
                    <FilterControls
                      initialFilters={currentFilters}
                      onFilterChange={(filters) => {
                        // Filtreleri uygulayıp state'e kaydediyoruz
                        handleApplyFilters(filters);
                        // Apply butonuna basılınca popup kapanır, Reset butonuna basınca da kapanır
                        setFilterDialogOpen(false);
                      }}
                      onClose={() => setFilterDialogOpen(false)}
                    />
                  </DialogContent>
                </Dialog>

                {}
                {/* Alert button reactivated for alert functionality */}
                <CreateAlertButton
                  className="h-10 w-full sm:w-auto
                    bg-[#132F4C]/50 border-[#1E4976]/30 text-[#E7EBF0]/80
                    hover:bg-[#132F4C]/70 hover:text-[#66B2FF] hover:border-[#1E4976]/50
                    focus:bg-[#132F4C]/70 focus:border-[#1E4976]/50
                    transition-all duration-200 rounded-md"
                  condition="both"
                />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0 relative">
          <div
            ref={tableContainerRef}
            className="overflow-x-auto w-full rounded-md scrollbar-custom pb-8 mx-auto"
            style={{ maxWidth: "100%", width: "100%" }}
          >
            <Table
              id="watchlist-table"
              className="w-full min-w-[940px] xl:min-w-0 xl:w-[1600px] max-w-[1600px] text-center mx-auto relative"
              responsive={true}
              containerClassName="!overflow-visible"
            >
              <CoinTableHeader
                sortConfig={sortConfig}
                requestSort={requestSort}
                id="coin-list-header"
                className="coin-list-header bg-[#132F4C]/90 hover:bg-[#132F4C]/90 backdrop-blur-sm  mt-2 sticky top-0 z-10 border-b border-[#1E4976]/50 shadow-md"
              />

              <TableBody className="relative">

                {paginatedCoins.map((coin: Coin, index: number) => (
                  <CoinTableRow
                    key={coin.id}
                    coin={coin}
                    index={index}
                    currentPage={currentPage}
                    pageSize={pageSize}
                    onRowClick={(coinId) => setLocation(`/coin/${coinId}`)}
                    showTrashIcon={false}
                  />
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <div
        id="pagination-section"
        className="mt-8 relative max-w-full mx-auto w-full"
      >
        <div className="absolute inset-x-0 top-[-10px] h-[1px] bg-[#1E4976]/20"></div>
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          pageSize={pageSize}
          totalItems={filteredCoins.length}
          onPageChange={setCurrentPage}
          onPageSizeChange={setPageSize}
          pageSizeOptions={[10, 20, 50, 100]}
          siblingsCount={1}
          className="mt-8 bg-[#0A1929]/10 pt-6 pb-8 rounded-lg"
        />
      </div>


    </div>
  );
}

const CoinListPageWithTour = () => {
  const [key, setKey] = React.useState(Date.now());
  const isMobile = useIsMobile();

  const memoizedContent = React.useMemo(() => {
    return (
      <div className="welcome-step">
        <CoinListPage />
        <div className="final-actions hidden">
          <Button>Create Watchlist</Button>
          <Button>Explore Trending</Button>
          <Button>View Top Gainers</Button>
        </div>
        {/* <CoinListTour /> */}
      </div>
    );
  }, [key]);

  React.useEffect(() => {
    console.log("CoinListPageWithTour mounted");

    const handleBeforeUnload = () => {
      sessionStorage.setItem(
        "coinListPageState",
        JSON.stringify({
          lastVisit: Date.now(),
        }),
      );
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    let isLocalStorageAccessible = true;
    try {
      localStorage.getItem("test");
    } catch (e) {
      isLocalStorageAccessible = false;
      console.error("localStorage is not accessible:", e);
    }

    const pageVisited =
      localStorage.getItem("coinlist-tour-visited") === "true";
    const tourCompleted =
      isTourCompleted("coinlist-tour") ||
      localStorage.getItem("coinlist-tour-completed") === "true";

    const shouldShowTour =
      isLocalStorageAccessible &&
      !tourCompleted &&
      !localStorage.getItem("coinlist-tour-dismissed") &&
      !sessionStorage.getItem("coinlist-tour-viewed");

    if (process.env.NODE_ENV === "development") {
      console.log("TOUR DEBUG - condition values:", {
        isLocalStorageAccessible,
        pageVisited,
        tourCompleted,
        tourDismissed: localStorage.getItem("coinlist-tour-dismissed"),
        tourViewedThisSession: sessionStorage.getItem("coinlist-tour-viewed"),
        shouldShowTour,
      });
    }

    const initialLoadDelay = 1200;

    if (shouldShowTour) {
      sessionStorage.setItem("coinlist-tour-viewed", "true");

      console.log("Tour conditions met - Showing tour once");

      setTimeout(() => {
        const event = new CustomEvent("show-coinlist-tour");
        document.dispatchEvent(event);
      }, initialLoadDelay);
    } else {
      console.log("Return visitor - not showing tour automatically");
    }

    return () => {
      console.log("CoinListPageWithTour unmounting");
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, []);

  return memoizedContent;
};

export default CoinListPageWithTour;
