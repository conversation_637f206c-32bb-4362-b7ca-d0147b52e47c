import { createContext, useContext, useEffect, useState, useCallback } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Language } from "@shared/schema";
import { getFontConfig, generateFontCustomProperties } from "@/lib/fonts";
import { useToast } from "@/hooks/use-toast";

interface LanguageContextType {
  currentLanguage: Language | null;
  availableLanguages: Language[];
  isLoading: boolean;
  setLanguage: (languageCode: string) => Promise<void>;
  translations: Record<string, string>;
  t: (key: string, namespace?: string, fallback?: string) => string;
  clearTranslationCache: () => void;
  refreshTranslations: () => Promise<void>;
  translationStats: {
    total: number;
    missing: string[];
    lastUpdated: string;
  };
}

// Fallback translations for critical UI elements in English
const FALLBACK_TRANSLATIONS: Record<string, string> = {
  "common:loading": "Loading...",
  "common:error": "Error",
  "common:retry": "Retry",
  "common:save": "Save",
  "common:cancel": "Cancel",
  "common:close": "Close",
  "common:success": "Success",
  "system.language.selector.choose": "Choose language",
  "system.language.selector.current": "Current language",
  "system.language.selector.searching": "Searching...",
  "system.error.translation": "Translation error",
  "nav:home": "Home",
  "nav:profile": "Profile",
  "nav:settings": "Settings",
  "nav:logout": "Logout",
  "nav:login": "Login",

  // Coinlist page translations
  "coinlist:allCoins": "All Coins",
  "coinlist:coinDetailDescription": "Click on any coin for detailed analysis",
  "coinlist:searchCoins": "Search coins...",
  "coinlist:searchBox.ariaLabel": "Search coins",
  "coinlist:aiPoweredTitle": "AI-Powered & Data-Driven Crypto Fundamental Ratings",
  "coinlist:comprehensiveAnalysis": "Comprehensive analysis and scoring of cryptocurrencies across",
  "coinlist:multipleMetrics": "multiple metrics",
  "coinlist:highlights": "Highlights",
  "coinlist:viewAll": "View All",
  "coinlist:currentPrice": "Current Price",
  "coinlist:marketCap": "Market Cap",
  "coinlist:rank": "Rank",
  "coinlist:filters.button": "Filters",
  "coinlist:filters.title": "Filter Options",
  "coinlist:filters.description": "Customize your view with advanced filtering options",
  "coinlist:columns.name": "Name",
  "coinlist:columns.tokenomics": "Tokenomics",
  "coinlist:columns.security": "Security",
  "coinlist:columns.social": "Social",
  "coinlist:columns.market": "Market",
  "coinlist:columns.insights": "Insights",
  "coinlist:columns.totalScore": "Total AI Score",
  "coinlist:columns.sevenDayChange": "7D Change"
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [currentLanguage, setCurrentLanguage] = useState<Language | null>(null);
  const [translations, setTranslations] = useState<Record<string, string>>({});
  const [missingKeys, setMissingKeys] = useState<string[]>([]);
  const [lastUpdated, setLastUpdated] = useState<string>(new Date().toISOString());
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Default languages to use if API fails or during development
  const defaultLanguages: Language[] = [
    { id: 1, code: 'en', name: 'English', nativeName: 'English', isRTL: false, isActive: true, createdAt: new Date(), updatedAt: new Date() },
    { id: 2, code: 'tr', name: 'Turkish', nativeName: 'Türkçe', isRTL: false, isActive: true, createdAt: new Date(), updatedAt: new Date() },
    { id: 3, code: 'es', name: 'Spanish', nativeName: 'Español', isRTL: false, isActive: true, createdAt: new Date(), updatedAt: new Date() },
    { id: 4, code: 'fr', name: 'French', nativeName: 'Français', isRTL: false, isActive: true, createdAt: new Date(), updatedAt: new Date() },
    { id: 5, code: 'de', name: 'German', nativeName: 'Deutsch', isRTL: false, isActive: true, createdAt: new Date(), updatedAt: new Date() },
    { id: 6, code: 'it', name: 'Italian', nativeName: 'Italiano', isRTL: false, isActive: true, createdAt: new Date(), updatedAt: new Date() },
    { id: 7, code: 'pt', name: 'Portuguese', nativeName: 'Português', isRTL: false, isActive: true, createdAt: new Date(), updatedAt: new Date() },
    { id: 8, code: 'ru', name: 'Russian', nativeName: 'Русский', isRTL: false, isActive: true, createdAt: new Date(), updatedAt: new Date() },
    { id: 9, code: 'ar', name: 'Arabic', nativeName: 'العربية', isRTL: true, isActive: true, createdAt: new Date(), updatedAt: new Date() },
    { id: 10, code: 'zh', name: 'Chinese', nativeName: '中文', isRTL: false, isActive: true, createdAt: new Date(), updatedAt: new Date() },
    { id: 11, code: 'ja', name: 'Japanese', nativeName: '日本語', isRTL: false, isActive: true, createdAt: new Date(), updatedAt: new Date() },
    { id: 12, code: 'ko', name: 'Korean', nativeName: '한국어', isRTL: false, isActive: true, createdAt: new Date(), updatedAt: new Date() },
    { id: 13, code: 'hi', name: 'Hindi', nativeName: 'हिन्दी', isRTL: false, isActive: true, createdAt: new Date(), updatedAt: new Date() },
    { id: 14, code: 'nl', name: 'Dutch', nativeName: 'Nederlands', isRTL: false, isActive: true, createdAt: new Date(), updatedAt: new Date() },
    { id: 15, code: 'pl', name: 'Polish', nativeName: 'Polski', isRTL: false, isActive: true, createdAt: new Date(), updatedAt: new Date() },
  ];

  // Fetch available languages, falling back to default languages if API fails
  const { data: availableLanguages = defaultLanguages, isLoading: isLoadingLanguages } = useQuery({
    queryKey: ["/api/languages"],
    queryFn: async () => {
      try {
        const response = await fetch("/api/languages");
        if (!response.ok) throw new Error("Failed to fetch languages");
        const languages = await response.json() as Language[];
        return languages.length > 0 ? languages : defaultLanguages;
      } catch (error) {
        console.log("Failed to fetch languages from API, using default languages");
        return defaultLanguages;
      }
    },
  });

  // Apply font configuration when language changes
  useEffect(() => {
    const fontConfig = getFontConfig(currentLanguage);
    const customProperties = generateFontCustomProperties(fontConfig);

    // Apply custom properties to document root
    Object.entries(customProperties).forEach(([property, value]) => {
      document.documentElement.style.setProperty(property, value);
    });

    // Set text direction on body
    document.body.dir = fontConfig.direction;
  }, [currentLanguage]);

  // Function to process translations into a format we can use
  const processTranslations = useCallback((data: any[]) => {
    // Process translations into a flat structure for easier lookup
    const processedTranslations = data.reduce((acc: Record<string, string>, t: any) => {
      // Create namespace:key format (e.g., "navigation:community")
      const colonFormat = `${t.namespace}:${t.key}`;
      acc[colonFormat] = t.value;

      // Also store with dot notation for backward compatibility
      const dotFormat = `${t.namespace}.${t.key}`;
      acc[dotFormat] = t.value;

      // Log only the first 10 translations to avoid console spam
      if (Object.keys(acc).length < 10) {
        console.log(`Adding translation: ${colonFormat} = "${t.value}"`);
      }

      return acc;
    }, {});

    // If we have the keys needed for the language selector UI but in the wrong format,
    // add them with the expected format
    Object.keys(processedTranslations).forEach(key => {
      if (key.startsWith('system:language.selector.')) {
        // Handle the special case for language selector UI
        const existingValue = processedTranslations[key];
        // Create a key that the LanguageSelector component expects
        const simpleKey = key.replace('system:language.selector.', 'system:language.selector:');
        processedTranslations[simpleKey] = existingValue;
      }
    });

    console.log("Processed translations:", Object.keys(processedTranslations).length);
    return processedTranslations;
  }, []);

  // Fetch translations for current language
  const { isLoading: isLoadingTranslations } = useQuery({
    queryKey: ["/api/languages/translations", currentLanguage?.id],
    enabled: !!currentLanguage,
    queryFn: async () => {
      console.log("Fetching translations for language ID:", currentLanguage?.id);
      const response = await fetch(`/api/languages/translations/${currentLanguage!.id}`);
      if (!response.ok) throw new Error("Failed to fetch translations");
      const data = await response.json();
      console.log("Received translations:", data.length > 0 ? `${data.length} items` : "empty array");

      // Process the translations outside of render
      const processedTranslations = processTranslations(data);

      // Use setTimeout to avoid state updates during render
      setTimeout(() => {
        setTranslations(processedTranslations);
      }, 0);

      return data;
    }
  });

  // Set language preference mutation
  const { mutateAsync: setLanguagePreference } = useMutation({
    mutationFn: async (languageCode: string) => {
      const response = await fetch("/api/languages/preference", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ languageCode }),
      });
      if (!response.ok) throw new Error("Failed to set language preference");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/languages/translations"] });
    },
  });

  // Initialize language from localStorage or browser preference
  useEffect(() => {
    if (availableLanguages.length === 0 || currentLanguage !== null) return;

    const savedLanguage = localStorage.getItem("preferredLanguage");
    const browserLanguage = navigator.language.split("-")[0];

    // Find language by code in available languages
    const findLanguage = (code: string) =>
      availableLanguages.find((lang) => lang.code === code);

    // Default to English (code 'en') if available, otherwise use first language
    const englishLanguage = availableLanguages.find((lang) => lang.code === 'en');
    const defaultLanguage = englishLanguage || availableLanguages[0];

    // Try to find saved language or browser language, fallback to default
    const language = savedLanguage
      ? findLanguage(savedLanguage) || defaultLanguage
      : findLanguage(browserLanguage) || defaultLanguage;

    if (language) {
      console.log(`Setting current language: ${language.name} (${language.code}, ID: ${language.id})`);
      setCurrentLanguage(language);
      localStorage.setItem("preferredLanguage", language.code);
    }
  }, [availableLanguages, currentLanguage]);

  const setLanguage = useCallback(async (languageCode: string) => {
    console.log("setLanguage called with:", languageCode);
    const language = availableLanguages.find((lang) => lang.code === languageCode);
    if (!language) {
      console.error("Language not found:", languageCode);
      return;
    }

    console.log("Changing to language:", language.name);

    try {
      // First, update the current language and local storage
      setCurrentLanguage(language);
      localStorage.setItem("preferredLanguage", languageCode);

      // Reset translations temporarily to avoid showing stale content
      setTranslations({});

      // Immediately invalidate and refetch translations for the new language
      await queryClient.invalidateQueries({
        queryKey: ["/api/languages/translations", language.id]
      });

      // Force refetch translations
      const response = await fetch(`/api/languages/translations/${language.id}`);
      if (!response.ok) throw new Error("Failed to fetch translations");
      const data = await response.json();

      // Process translations
      const processedTranslations = processTranslations(data);
      setTranslations(processedTranslations);

      // Attempt to save preference to server (will fail gracefully if user is not logged in)
      try {
        await setLanguagePreference(languageCode);
      } catch (error) {
        console.log("User not logged in, skipping server-side language preference save");
        // This is fine - we'll just rely on localStorage when not logged in
      }

      console.log("Language changed successfully to:", language.name);
      return true;
    } catch (error) {
      console.error("Error changing language:", error);
      return false;
    }
  }, [availableLanguages, queryClient, processTranslations, setLanguagePreference]);

  // Clear translation cache and force refresh
  const clearTranslationCache = useCallback(() => {
    if (!currentLanguage) return;

    // Clear in-memory translations
    setTranslations({});

    // Force invalidate and refetch
    queryClient.invalidateQueries({
      queryKey: ["/api/languages/translations", currentLanguage.id]
    });

    toast({
      title: "Translation cache cleared",
      description: "Reloading translations...",
      duration: 3000
    });
  }, [currentLanguage, queryClient, toast]);

  // Manually refresh translations
  const refreshTranslations = useCallback(async () => {
    if (!currentLanguage) return;

    setLastUpdated(new Date().toISOString());

    try {
      await queryClient.invalidateQueries({
        queryKey: ["/api/languages/translations", currentLanguage.id]
      });

      toast({
        title: "Translations refreshed",
        description: `Loaded ${Object.keys(translations).length} translations`,
        duration: 3000
      });
    } catch (error) {
      console.error("Failed to refresh translations:", error);
      toast({
        title: "Failed to refresh translations",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
        duration: 5000
      });
    }
  }, [currentLanguage, queryClient, toast, translations]);

  // Enhanced translation function with fallback support
  const t = useCallback((key: string, namespace: string = "common", fallback?: string) => {
    // Create all possible key formats to look up
    const colonFormat = `${namespace}:${key}`;
    const dotFormat = `${namespace}.${key}`;

    // Try all possible formats
    const possibleKeys = [colonFormat, dotFormat, key];

    // For debugging only
    const debug = false;
    if (debug) {
      if (Object.keys(translations).length > 0) {
        console.log(`Translation lookup for key: ${colonFormat}`, {
          colonFound: !!translations[colonFormat],
          dotFound: !!translations[dotFormat],
          directFound: !!translations[key],
          totalKeys: Object.keys(translations).length
        });
      } else {
        console.log(`Translation requested for key: ${colonFormat}`, {
          available: Object.keys(translations).length,
          keys: Object.keys(translations).slice(0, 5)
        });
      }
    }

    // Try all formats in the active translations
    for (const format of possibleKeys) {
      if (translations[format]) {
        return translations[format];
      }
    }

    // If not found in active translations, check fallback translations
    for (const format of possibleKeys) {
      if (FALLBACK_TRANSLATIONS[format]) {
        // Instead of updating state during render, schedule it for after render
        if (!import.meta.env.DEV) {
          // Use setTimeout to avoid state updates during render
          setTimeout(() => {
            if (!missingKeys.includes(format)) {
              setMissingKeys(prev => [...prev, format]);
            }
          }, 0);
        }
        return FALLBACK_TRANSLATIONS[format];
      }
    }

    // If provided with a explicit fallback, use it
    if (fallback !== undefined) {
      return fallback;
    }

    // As a last resort, return the key itself
    // Schedule state update to avoid doing it during render
    setTimeout(() => {
      if (!missingKeys.includes(colonFormat)) {
        setMissingKeys(prev => [...prev, colonFormat]);
      }
    }, 0);

    return key;
  }, [translations, missingKeys]);

  return (
    <LanguageContext.Provider
      value={{
        currentLanguage,
        availableLanguages,
        isLoading: isLoadingLanguages || isLoadingTranslations,
        setLanguage,
        translations,
        t,
        clearTranslationCache,
        refreshTranslations,
        translationStats: {
          total: Object.keys(translations).length,
          missing: missingKeys,
          lastUpdated
        }
      }}
    >
      {children}
    </LanguageContext.Provider>
  );
}

// Define the hook as a named function declaration instead of an arrow function
// This helps with HMR (Hot Module Replacement) compatibility
export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
}